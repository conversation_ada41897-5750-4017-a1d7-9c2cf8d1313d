job "atomic-bm" {
  datacenters = ["mktd"]
  type        = "service"

  group "backend" {
    network {
      port "backend-api-http" {
        to = 8000
      }
    }

    task "backend" {
      driver = "docker"

      config {
        image = "sjc.vultrcr.com/atomcr/botmanager:v1.12.0"
        ports = ["backend-api-http"]
        args  = ["server"]
      }

      restart {
        attempts = 5
        interval = "10s"
        delay    = "15s"
        mode     = "fail"
      }

      template {
        data = <<EOH
{{ with nomadVar "nomad/jobs/atomic-bm/backend" }}
# GIN
debug: false

# DB
#  bun_debug=0 - disables the hook.
#  bun_debug=1 - enables the hook.
#  bun_debug=2 - enables the hook and verbose mode.
bun_debug: 0

# Server
log:
  level: "INFO"

database:
  dsn: "{{ .POSTGRES_DNS }}"

redis:
  addr: "redis-db.service.consul:6379"

listen:
  addr: "0.0.0.0:8000"

session_manager_cfg:
  idle_timeout: 0
  lifetime: 24
  cleanup_interval: 0
  cookie:
    name: session_id
    domain: api-term.htz-fsn-02.liquidbooks.io
    http_only: true
    path: /
    persist: true
    same_site: 1
    secure: false

bot_regional_servers:
  - region_id: "local"
    api_key: "{{ .BR_LOCAL_API_KEY }}"
    insecure: false
    bot_regional_addr: "atomic-br.dev.htz-fsn-02.liquidbooks.io:443"

open_telemetry:
  service_name: botmanager
  exporter_url: "otel.htz-fsn-02.liquidbooks.io:443"
  bearer_token: "{{ .OTEL_BEARER_TOKEN }}"
  traces:
    enabled: true
    sample_rate: 1.0
  metrics:
    enabled: true
  logs:
    enabled: true

param_secret_key: "{{ .PARAM_SECRET_KEY }}"
param_secret_key_version: 1

firebase:
  type: "service_account"
  project_id: "atomic-bm-tabs"
  private_key_id: "13694cc85db2048122fc03598ab9f0de39cd95e6"
  private_key: >
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: "<EMAIL>"
  client_id: "114384668544031984245"
  auth_uri: "https://accounts.google.com/o/oauth2/auth"
  token_uri: "https://oauth2.googleapis.com/token"
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs"
  client_x509_cert_url: "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-iddol%40atomic-bm-tabs.iam.gserviceaccount.com"
  universe_domain: "googleapis.com"
{{ end }}
EOH
        destination = "secrets/config.yaml"
      }

      env {
        GIN_MODE    = "release"
        CONFIG_PATH = "/secrets"
      }

      service {
        name = "botmanager-api"
        port = "backend-api-http"
        address_mode = "driver"

        check {
          name     = "BM HTTP"
          type     = "http"
          path     = "/api/v1/health"
          interval = "10s"
          timeout  = "1s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.botmanager-api.rule=Host(`api-term.htz-fsn-02.liquidbooks.io`)",
          "traefik.http.routers.botmanager-api.entrypoints=https",
          "traefik.http.routers.botmanager-api.tls=true",
          "traefik.http.services.botmanager-api.loadbalancer.server.port=8000"
        ]
      }

      resources {
        cpu    = 2000 # 2 CPUs
        memory = 2048 # 2 GB
      }
    }
  }

  group "psql" {
    volume "psql-data" {
      type      = "host"
      read_only = false
      source    = "atomic-bm-psql-data"
    }

    network {
      port "psql" {
        to = 5432
        static = 5432
      }
    }

    task "psql" {
      driver = "docker"
      config {
        image = "bitnami/postgresql:16"
        ports = ["psql"]
      }

      volume_mount {
        volume      = "psql-data"
        destination = "/bitnami/postgresql/data"
      }

      service {
        name = "postgres-db"
        port = "psql"
        address_mode = "driver"
      }

      resources {
        cpu    = 4000 # 4 CPUs
        memory = 4096 # 4 GB
      }

      template {
        data = <<EOH
{{ with nomadVar "nomad/jobs/atomic-bm/psql" }}
POSTGRESQL_PASSWORD="{{ .POSTGRES_PASSWORD }}"
POSTGRESQL_DATABASE=botmanager
{{ end }}
EOH
        destination = "secrets/db.env"
        env         = true
      }

      template {
        data = <<EOH
listen_addresses = '*'
port = '5432'
max_connections = '100'
shared_preload_libraries = 'pgaudit'
wal_level = 'replica'
max_wal_senders = '16'
wal_keep_size = '128MB'
hot_standby = 'on'
client_min_messages = 'error'
EOH
        destination = "/opt/bitnami/postgresql/conf/postgresql.conf"
        change_mode   = "signal"
        change_signal = "SIGINT"
      }

      template {
        data = <<EOH
# host all all 0.0.0.0/0 md5
# host all all ::/0 md5
# The above is a common setting but for security, you might want to be more specific.
# For the purpose of this file, we'll keep the provided content.
host all all 0.0.0.0/0 md5
host all all ::/0 md5
EOH
        destination = "/opt/bitnami/postgresql/conf/pg_hba.conf"
      }
    }
  }

  group "redis" {
    volume "redis-data" {
      type      = "host"
      read_only = false
      source    = "atomic-bm-redis-data"
    }

    network {
      port "redis" {
        to = 6379
      }
    }

    task "redis" {
      driver = "docker"
      config {
        image = "bitnami/redis:7.4.2-debian-12-r9"
        ports = ["redis"]
      }

      volume_mount {
        volume      = "redis-data"
        destination = "/bitnami/redis/data"
      }

      env {
        ALLOW_EMPTY_PASSWORD = "yes"
      }

      template {
        data = <<EOH
bind 0.0.0.0 ::
protected-mode no
port 6379
appendonly yes
appendfilename "appendonly.aof"
appenddirname "appendonlydir"
EOH
        destination = "/opt/bitnami/redis/etc/redis.conf"
        change_mode   = "signal"
        change_signal = "SIGINT"
      }

      service {
        name = "redis-db"
        port = "redis"
        address_mode = "driver"
      }

      resources {
        cpu    = 2000 # 2 CPUs
        memory = 1024 # 2 GB
      }
    }
  }
}