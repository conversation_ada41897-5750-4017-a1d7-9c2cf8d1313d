job "atomic-bm-migrate" {
  datacenters = ["mktd"]
  type        = "batch"

  group "migrate" {
    task "migrate" {
      driver = "docker"

      config {
        image = "sjc.vultrcr.com/atomcr/botmanager:v1.12.0"
        args  = ["migrate", "up"]
      }

      template {
        data = <<EOH
{{ with nomadVar "nomad/jobs/atomic-bm-migrate/migrate" }}
database:
  dsn: "{{ .POSTGRES_DNS }}"
{{ end }}
EOH
        destination = "secrets/config.yaml"
      }

      restart {
        attempts = 0
        interval = "10s"
        delay    = "15s"
        mode     = "fail"
      }
    }
  }
}
