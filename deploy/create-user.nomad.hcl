job "atomic-bm-create-user" {
  datacenters = ["mktd"]
  type        = "batch"

  group "create-user" {
    count = 1

    task "create-user" {
      driver = "docker"

      config {
        image = "sjc.vultrcr.com/atomcr/botmanager:v1.11.1"
        args  = [
          "create-user",
          "--name=",
          "--password=",
          "--email=",
          "--role=",
        ]
      }

      env {
        CONFIG_PATH = "/secrets"
      }

      template {
        data = <<EOH
{{ with nomadVar "nomad/jobs/atomic-bm-create-user/create-user" }}
database:
  dsn: "{{ .POSTGRES_DNS }}"

log:
  level: "DEBUG"

{{ end }}
EOH
        destination = "secrets/config.yaml"
      }

      resources {
        cpu    = 500
        memory = 256
      }
    }
  }
}
