## [1.12.0](https://github.com/herenow/atomic-bm/compare/v1.11.1...v1.12.0) (2025-08-06)

### ✨ Features

* **config:** add session management and db pooling ([11eac48](https://github.com/herenow/atomic-bm/commit/11eac486c64a8193dc517b7a4a1a69211340ae4f))

### 📦 Chores

* **deploy:** update nomad job files and ignore GEMINI.md ([dc02539](https://github.com/herenow/atomic-bm/commit/dc025397c99b856546ebd30fe3337385187b8500))

## [1.11.1](https://github.com/herenow/atomic-bm/compare/v1.11.0...v1.11.1) (2025-08-05)

### 🐛 Bug Fixes

* restore health check endpoint and update deployment files ([1751f96](https://github.com/herenow/atomic-bm/commit/1751f9624209902e1988f2d7e0739c94995507b9))

## [1.11.0](https://github.com/herenow/atomic-bm/compare/v1.10.5...v1.11.0) (2025-08-05)

### ✨ Features

* **atomic-protocols:** Update submodule to latest changes ([fbb8802](https://github.com/herenow/atomic-bm/commit/fbb88021961597329aee777c5870451e6ea636df))
* **botregional:** Refactor bot regional client and remove submodules ([2201397](https://github.com/herenow/atomic-bm/commit/220139778c44993a09d8f743e83912d0b85feb6f))
* **nomad:** add insecure flag to bot regional servers and static port to psql ([1fe8753](https://github.com/herenow/atomic-bm/commit/1fe8753662d11dc06f738e5be77049e87758b664))

### 🐛 Bug Fixes

* dead lock in get bot state on br ([3a17930](https://github.com/herenow/atomic-bm/commit/3a179307643ba6879cfddfcdfc8360c01c3a6056))
* gin cors ([82107f3](https://github.com/herenow/atomic-bm/commit/82107f3a59d6cc415e6b86f710df4d5be2c9fd1f))
* git submodules ([7868394](https://github.com/herenow/atomic-bm/commit/7868394a638b4aed10d93ed325f84c511ad553f6))
* **migrate:** fix typo on migration cli ([e2675aa](https://github.com/herenow/atomic-bm/commit/e2675aae54f38a65fd9f843555da83efae059a41))
* swagger docs ([29a1759](https://github.com/herenow/atomic-bm/commit/29a175978548689aee3d0041e3a3ca6c0445cd33))

### ♻️ Refactor

* **br:** move br to outside bm internal code ([f8009d9](https://github.com/herenow/atomic-bm/commit/f8009d9b41bc8f6303360962f1246e7fab1ddb57))
* **br:** now we are integrating a concrete bot instead of mocking ([5781d32](https://github.com/herenow/atomic-bm/commit/5781d323d19cfb10ea076cd227b9158360c049a6))

### 📦 Chores

* added marketdata package to br ([80d7802](https://github.com/herenow/atomic-bm/commit/80d7802627c6cdfb01d082eec097e72205b4ca0e))
* added orderbook to get bots state ([057c3f6](https://github.com/herenow/atomic-bm/commit/057c3f688e344d9b4da11f5b8d9dcfe4cc8d2cd4))
* adjust bot states tracking for front-end data ([3abe9ea](https://github.com/herenow/atomic-bm/commit/3abe9eae58736990ebdb1afe3f673154a6742c22))
* adjust contracts and botregional proto ([bde92cb](https://github.com/herenow/atomic-bm/commit/bde92cbc5c1e5f5f40133be65af21dae2fa35ffe))
* adjust operator and br grpc protocol ([d23db35](https://github.com/herenow/atomic-bm/commit/d23db35a94e4e49d5965be4dffa3e68913e68c1a))
* **bots:** adjust bots service ([3657863](https://github.com/herenow/atomic-bm/commit/365786358ed6cb2dd574066f6c71c5ca41bcf4d2))
* **bots:** adjust docs gen ([5a0a1b4](https://github.com/herenow/atomic-bm/commit/5a0a1b4a2e9e705f9860231f07fb7d664564d641))
* **br:** adjust br and client to support gtw updates ([a9dfb41](https://github.com/herenow/atomic-bm/commit/a9dfb4167b02d780cb6e0bee9b5cada667758d1d))
* **br:** integrate order manager ([c581b25](https://github.com/herenow/atomic-bm/commit/c581b2573a178012cd3e3db21958a7253e4a5e89))
* change branch protocols to master ([33e1ed7](https://github.com/herenow/atomic-bm/commit/33e1ed7b1a90d0053abf9daaa653d4ff2c201d3a))
* **ci:** adjust ci and fixed deploy files ([323104f](https://github.com/herenow/atomic-bm/commit/323104f21f4301f8c7afe85cf0e0ceba32348053))
* go mod tidy ([3189781](https://github.com/herenow/atomic-bm/commit/3189781c5c4542ece863ed4d12a8db5144018acc))
* go mod tidy ([df04e89](https://github.com/herenow/atomic-bm/commit/df04e899566386803457a791a478aac1277aa014))
* **go:** add go.work to the project ([7480839](https://github.com/herenow/atomic-bm/commit/74808395604cd832e05f28f70daf6f6ec9e0ae6d))
* **otel:** simplified otel packages ([45a48ae](https://github.com/herenow/atomic-bm/commit/45a48ae9326811732b2016be9288b8143258abfc))
* **params:** adjust create params ([c591a5e](https://github.com/herenow/atomic-bm/commit/c591a5eeb330a57792f68afbc20f48fa0ef74da1))
* **proto:** sync ([36f55d4](https://github.com/herenow/atomic-bm/commit/36f55d44a60b012d4c023e037c7a6aa272a5b554))
* update git submodules ([c1aea41](https://github.com/herenow/atomic-bm/commit/c1aea41ceecca974001188804cd616d1dd1c9708))
* update submodules ([cec3965](https://github.com/herenow/atomic-bm/commit/cec39655eed0a03031ac80880587ba96023bb6d2))
* wip ([962705b](https://github.com/herenow/atomic-bm/commit/962705b91322450dac04720c8169fbc8fc12967b))

## [1.10.5](https://github.com/herenow/atomic-bm/compare/v1.10.4...v1.10.5) (2025-06-26)

### 🐛 Bug Fixes

* **config:** adjusting config prod path ([d7260bd](https://github.com/herenow/atomic-bm/commit/d7260bd74a14f9b219ffb013d7683cc7dbc36cd0))

## [1.10.4](https://github.com/herenow/atomic-bm/compare/v1.10.3...v1.10.4) (2025-06-25)

### 🐛 Bug Fixes

* **controller:** controller was not being instantiated ([68ce058](https://github.com/herenow/atomic-bm/commit/68ce05878c9f43f520b6269783c38890bd4aa7e7))

## [1.10.3](https://github.com/herenow/atomic-bm/compare/v1.10.2...v1.10.3) (2025-06-25)

### 📦 Chores

* **ci:** adjust ci update version script ([683ca90](https://github.com/herenow/atomic-bm/commit/683ca90cf5ae19c7aac4e4750989aca92828b802))

## [1.10.2](https://github.com/herenow/atomic-bm/compare/v1.10.1...v1.10.2) (2025-06-25)

### 📦 Chores

* **ci:** adjust ci to dynamically updated version and gen docs ([5eed306](https://github.com/herenow/atomic-bm/commit/5eed30675bf7aa512ba2bd966be98fcbdebbb653))

## [1.10.1](https://github.com/herenow/atomic-bm/compare/v1.10.0...v1.10.1) (2025-06-24)

### 📦 Chores

* big beautiful build changing in this project ([168feed](https://github.com/herenow/atomic-bm/commit/168feed76c8bfab5f5474a55790f0387c0c45342))
* **otel:** added bearer token for auth ([2879b1b](https://github.com/herenow/atomic-bm/commit/2879b1b86029c7abbff9495ef74a3f69f0dd4dc8))

## [1.10.1](https://github.com/herenow/atomic-bm/compare/v1.10.0...v1.10.1) (2025-06-24)

### 📦 Chores

* big beautiful build changing in this project ([168feed](https://github.com/herenow/atomic-bm/commit/168feed76c8bfab5f5474a55790f0387c0c45342))
* **otel:** added bearer token for auth ([2879b1b](https://github.com/herenow/atomic-bm/commit/2879b1b86029c7abbff9495ef74a3f69f0dd4dc8))
