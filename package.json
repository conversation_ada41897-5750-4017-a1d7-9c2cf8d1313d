{"name": "bm-service", "version": "1.12.0", "description": "Bot Manager Service", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "semantic-release": "semantic-release", "update-versions": "node update-versions.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.1.3", "@semantic-release/release-notes-generator": "^14.0.1", "conventional-changelog-conventionalcommits": "^9.0.0", "pnpm": "^9.5.0", "semantic-release": "^24.0.0"}}