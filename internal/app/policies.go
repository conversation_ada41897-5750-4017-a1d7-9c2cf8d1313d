package app

import (
	"github.com/gin-gonic/gin"
	"github.com/herenow/atomic-bm/internal/auth/casbin"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

func (app *App) Authorizer() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip login path
		if c.FullPath() == "/api/v1/users/login" {
			c.Next()
			return
		}

		spanCtx, span := app.tracer.Start(c.Request.Context(), "Authorizer/LoadFromQueryParam")

		s := app.Session()

		userID := s.UserIDFromContext(c)
		if userID == "" {
			userID = casbin.GuestUser
		}

		// Get the path and HTTP method from the request.
		path := c.Request.URL.Path
		method := c.Request.Method

		// Check if the user's has permission to access the requested resource.
		allowed, err := app.Policy().Enforce(userID, path, method)
		if err != nil {
			ginx.ResError(c, errors.Wrap(err, "failed when enforcing policy"))
			return
		}

		if !allowed {
			ginx.ResError(c, errors.ForbiddenResourceError)
			return
		}

		// Propagate the span context in the Gin context for subsequent handlers
		c.Request = c.Request.WithContext(spanCtx)

		span.End()

		c.Next()
	}
}
