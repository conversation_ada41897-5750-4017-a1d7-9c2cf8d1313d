package app

import (
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/herenow/atomic-bm/internal/pkg/cache"
	"github.com/spf13/viper"

	"github.com/herenow/atomic-bm/internal/auth/session"
	pushnotify "github.com/herenow/atomic-bm/pkg/firebase/fcm"
)

type Config struct {
	Service string `mapstructure:"-"`

	BunDebug string `mapstructure:"bun_debug"`

	// App debug
	Debug bool

	Listen struct {
		Addr string
	}

	Database struct {
		DSN string
	}

	BotRegionalServers []struct {
		RegionID        string `mapstructure:"region_id"`
		Insecure        bool   `mapstructure:"insecure"`
		ApiKey          string `mapstructure:"api_key"`
		BotRegionalAddr string `mapstructure:"bot_regional_addr"`
	} `mapstructure:"bot_regional_servers"`

	Log struct {
		Level string
	}

	SessionManager session.ManagerConfig `mapstructure:"session_manager_cfg"`

	// GRPC server configuration
	GRPC struct {
		Bind string
	} `mapstructure:"grpc"`

	// ParamSecretKey is the secret key used for encrypting and decrypting
	// sensitive parameter values, such as 'token' and 'api_secret'.
	//
	// This key should be a Base64-encoded 256-bit (32-byte) key. It is crucial
	// to keep this key secure as it is used for cryptographic operations.
	//
	// Please generate a strong and unique key using the crypto.NewEncryptionKey function.
	ParamSecretKey string `mapstructure:"param_secret_key"`
	// ParamSecretKeyVersion controls the version of encryption applied to parameters.
	// This allows for future updates, such as changing the paramSecretKey while
	// maintaining compatibility with different encryption versions.
	ParamSecretKeyVersion int `mapstructure:"param_secret_key_version"`

	PushNotify pushnotify.FirebaseConfig `mapstructure:"firebase"`

	Redis struct {
		Addr string
	} `mapstructure:","`

	Cache cache.Config `mapstructure:"cache"`

	OpenTelemetry struct {
		ServiceName string `mapstructure:"service_name"`
		BearerToken string `mapstructure:"bearer_token"`
		ExporterURL string `mapstructure:"exporter_url"`
		Traces      struct {
			Enabled    bool
			SampleRate float64 `mapstructure:"sample_rate"`
			Insecure   bool
		} `mapstructure:","`
		Metrics struct {
			Enabled  bool
			Insecure bool
		} `mapstructure:","`
		Logs struct {
			Enabled  bool
			Insecure bool
		} `mapstructure:","`
	} `mapstructure:"open_telemetry"`
}

func ReadConfig(service string) (*Config, error) {
	// Initialize Viper
	v := viper.New()

	// Config name and extension type accepted
	v.SetConfigName("config")
	v.SetConfigType("yaml")

	// Highest priority (Prod)
	v.AddConfigPath("/secrets")

	path, _ := os.LookupEnv("CONFIG_PATH")
	v.AddConfigPath(path)

	// Lowest priority (Development)
	v.AddConfigPath(".")

	// Environment variable settings
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_"))
	v.AllowEmptyEnv(true)
	v.AutomaticEnv()

	// Read the configuration file
	var configFileNotFoundError viper.ConfigFileNotFoundError
	if err := v.ReadInConfig(); err != nil {
		if errors.As(err, &configFileNotFoundError) {
			fmt.Printf("config not found: %v \n", err)
		}
		return nil, err
	}

	// Define the configuration structure
	var cfg Config
	if err := v.Unmarshal(&cfg); err != nil {
		return nil, err
	}

	cfg.Service = service

	return &cfg, nil
}
