package paramsservice

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	accRepo "github.com/herenow/atomic-bm/internal/db/repo/accountrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/botregionalclient"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/paramrepo"
)

type MockSetup struct {
	BotResponse *botrepo.Bot
	BotError    error

	BotParamGroupsResponse []paramrepo.BotParamGroup
	BotParamGroupsError    error

	ParamsResponse []paramrepo.Param
	ParamsError    error

	ParamGroupsResponse []paramrepo.ParamGroup
	ParamGroupsError    error
}

func setupMocks(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, setup MockSetup) {
	// Mock Get for bot
	mockBotsRepo.EXPECT().Get(mock.Anything, mock.Anything, mock.Anything).Return(setup.BotResponse, setup.BotError)

	// Mock ListBotParamGroupsRelations
	mockParamsRepo.EXPECT().ListBotParamGroupsRelations(mock.Anything, mock.Anything).
		Return(&paramrepo.ListBotParamGroupsResponse{
			BotParamGroups: setup.BotParamGroupsResponse,
		}, setup.BotParamGroupsError)

	// Mock List for params
	mockParamsRepo.EXPECT().List(mock.Anything, mock.Anything).Return(
		&paramrepo.ListParamsResponse{
			Params: setup.ParamsResponse,
		}, setup.ParamsError)

	// Mock ListParamGroups
	mockParamsRepo.EXPECT().ListParamGroups(mock.Anything, mock.Anything).Return(
		&paramrepo.ListParamGroupsResponse{
			ParamGroups: setup.ParamGroupsResponse},
		setup.ParamGroupsError)
}

func TestService_GetActiveParams(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name           string
		request        GetActiveParamsRequest
		setupMocks     func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked)
		expectedResult *GetActiveParamsResponse
		expectedError  error
	}{
		{
			name: "Bot Scope win the Precedence",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{
							{BotID: "bot1", ParamGroupsID: "group1"},
						},
						BotParamGroupsError: nil,

						ParamsResponse: []paramrepo.Param{
							{Scope: "gateway", ScopeID: "account1", Key: "minSpread", Value: "1", CreatedAt: now},
							{Scope: "account", ScopeID: "account1", Key: "minSpread", Value: "2", CreatedAt: now},
							{Scope: "param_group", ScopeID: "group1", Key: "minSpread", Value: "3", CreatedAt: now},
							{Scope: "bot", ScopeID: "bot1", Key: "minSpread", Value: "99", CreatedAt: now},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{
							{ID: "group1", Priority: 1},
						},
						ParamGroupsError: nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{ScopeID: "bot1", Scope: "bot", Key: "minSpread", Value: "99", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
		{
			name: "Param group win the Precedence",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{
							{BotID: "bot1", ParamGroupsID: "group1"},
						},
						BotParamGroupsError: nil,

						ParamsResponse: []paramrepo.Param{
							{Scope: "gateway", ScopeID: "account1", Key: "minSpread", Value: "1", CreatedAt: now},
							{Scope: "account", ScopeID: "account1", Key: "minSpread", Value: "2", CreatedAt: now},
							{Scope: "param_group", ScopeID: "group1", Key: "minSpread", Value: "99", CreatedAt: now},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{
							{ID: "group1", Priority: 1},
						},
						ParamGroupsError: nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{Scope: "param_group", ScopeID: "group1", Key: "minSpread", Value: "99", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
		{
			name: "Account win the Precedence",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{
							{BotID: "bot1", ParamGroupsID: "group1"},
						},
						BotParamGroupsError: nil,

						ParamsResponse: []paramrepo.Param{
							{Scope: "gateway", ScopeID: "account1", Key: "minSpread", Value: "1", CreatedAt: now},
							{Scope: "account", ScopeID: "account1", Key: "minSpread", Value: "99", CreatedAt: now},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{
							{ID: "group1", Priority: 1},
						},
						ParamGroupsError: nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{ScopeID: "account1", Scope: "account", Key: "minSpread", Value: "99", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
		{
			name: "Gateway win the Precedence",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{
							{BotID: "bot1", ParamGroupsID: "group1"},
						},
						BotParamGroupsError: nil,

						ParamsResponse: []paramrepo.Param{
							{Scope: "gateway", ScopeID: "account1", Key: "minSpread", Value: "1", CreatedAt: now},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{
							{ID: "group1", Priority: 1},
						},
						ParamGroupsError: nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{ScopeID: "account1", Scope: "gateway", Key: "minSpread", Value: "1", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
		{
			name: "Group with highest priority wins",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{
							{BotID: "bot1", ParamGroupsID: "group1"},
							{BotID: "bot1", ParamGroupsID: "group2"},
							{BotID: "bot1", ParamGroupsID: "group3"},
						},
						BotParamGroupsError: nil,

						ParamsResponse: []paramrepo.Param{
							{Scope: "param_group", ScopeID: "group1", Key: "minSpread", Value: "1", CreatedAt: now},
							{Scope: "param_group", ScopeID: "group2", Key: "minSpread", Value: "2", CreatedAt: now},
							{Scope: "param_group", ScopeID: "group3", Key: "minSpread", Value: "3", CreatedAt: now},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{
							{ID: "group1", Priority: 1},
							{ID: "group2", Priority: 2},
							{ID: "group3", Priority: 3},
						},
						ParamGroupsError: nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{ScopeID: "group3", Scope: "param_group", Key: "minSpread", Value: "3", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
		{
			name: "Group with same priority win the latest param created at",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{
							{BotID: "bot1", ParamGroupsID: "group1"},
							{BotID: "bot1", ParamGroupsID: "group2"},
							{BotID: "bot1", ParamGroupsID: "group3"},
						},
						BotParamGroupsError: nil,

						ParamsResponse: []paramrepo.Param{
							{Scope: "param_group", ScopeID: "group1", Key: "minSpread", Value: "1", CreatedAt: now},
							{Scope: "param_group", ScopeID: "group2", Key: "minSpread", Value: "2", CreatedAt: now.Add(-time.Hour)},
							{Scope: "param_group", ScopeID: "group3", Key: "minSpread", Value: "3", CreatedAt: now.Add(-time.Hour)},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{
							{ID: "group1", Priority: 1},
							{ID: "group2", Priority: 1},
							{ID: "group3", Priority: 1},
						},
						ParamGroupsError: nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{ScopeID: "group1", Scope: "param_group", Key: "minSpread", Value: "1", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
		{
			name: "Account scope with same precedence wins by latest CreatedAt",
			request: GetActiveParamsRequest{
				BotID: "bot1",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked) {
				setupMocks(
					mockBotsRepo,
					mockParamsRepo,
					MockSetup{
						BotResponse: &botrepo.Bot{ID: "bot1", AccountID: "account1"},
						BotError:    nil,

						BotParamGroupsResponse: []paramrepo.BotParamGroup{},
						BotParamGroupsError:    nil,

						ParamsResponse: []paramrepo.Param{
							// This older parameter should lose the tie-break
							{Scope: "account", ScopeID: "account1", Key: "minSpread", Value: "10", CreatedAt: now.Add(-time.Hour)},
							// This newer parameter should win the tie-break
							{Scope: "account", ScopeID: "account1", Key: "minSpread", Value: "20", CreatedAt: now},
						},
						ParamsError: nil,

						ParamGroupsResponse: []paramrepo.ParamGroup{},
						ParamGroupsError:    nil,
					},
				)
			},
			expectedResult: &GetActiveParamsResponse{
				Params: []Param{
					{ScopeID: "account1", Scope: "account", Key: "minSpread", Value: "20", CreatedAt: now},
				},
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParamsRepo := paramrepo.NewIParamsMocked(t)
			mockBotsRepo := botrepo.NewIBotsMocked(t)
			mockAccountRepo := accRepo.NewIAccountsMocked(t)
			mockBotRegionalCLient := botregionalclient.NewIBotRegionalClientMocked(t)

			tt.setupMocks(mockBotsRepo, mockParamsRepo)

			paramSvc := New(mockBotsRepo, mockParamsRepo, mockAccountRepo, "tooSecret", 1)

			result, err := paramSvc.GetActiveParams(context.Background(), tt.request)

			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedResult, result)

			mockBotsRepo.AssertExpectations(t)
			mockParamsRepo.AssertExpectations(t)
		})
	}
}

func TestService_Delete(t *testing.T) {
	userID := uuid.New().String()
	botID := uuid.New().String()
	accountID := uuid.New().String()
	groupID := uuid.New().String()

	tests := []struct {
		name          string
		request       DeleteParamsRequest
		setupMocks    func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked)
		expectedError error
	}{
		{
			name: "Successful delete of bot scope param",
			request: DeleteParamsRequest{
				UserID:  userID,
				ScopeID: botID,
				Scope:   BotScope.String(),
				Keys:    []string{"minSpread"},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{Total: 1, Params: []paramrepo.Param{{ScopeID: botID}}}, nil).Once()
				mockParamsRepo.On("Create", mock.Anything, mock.Anything).Return(nil).Once()
				mockBotsRepo.On("Get", mock.Anything, botID, true).Return(&botrepo.Bot{ID: botID, Active: true, Account: accRepo.Account{RegionID: "region-1"}}, nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, mock.Anything).Return(&paramrepo.ListBotParamGroupsResponse{}, nil).Once()
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{}, nil).Once()
				mockParamsRepo.On("ListParamGroups", mock.Anything, mock.Anything).Return(&paramrepo.ListParamGroupsResponse{}, nil).Once()
				mockBotRegionalCli.On("UpdateBotParam", mock.Anything, "region-1", mock.Anything).Return(nil).Once()
			},
			expectedError: nil,
		},
		{
			name: "Successful delete of account scope param",
			request: DeleteParamsRequest{
				UserID:  userID,
				ScopeID: accountID,
				Scope:   AccountScope.String(),
				Keys:    []string{"minSpread"},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{Total: 1}, nil).Once()
				mockParamsRepo.On("Create", mock.Anything, mock.Anything).Return(nil).Once()
				mockBotsRepo.On("List", mock.Anything, botrepo.ListBotsRequest{AccountID: accountID, WithAccount: true}).Return(&botrepo.ListBotsResponse{Bots: []*botrepo.Bot{{ID: botID, Active: true, Account: accRepo.Account{RegionID: "region-1"}}}}, nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, mock.Anything).Return(&paramrepo.ListBotParamGroupsResponse{}, nil).Once()
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{}, nil).Once()
				mockParamsRepo.On("ListParamGroups", mock.Anything, mock.Anything).Return(&paramrepo.ListParamGroupsResponse{}, nil).Once()
				mockBotRegionalCli.On("UpdateBotParam", mock.Anything, "region-1", mock.Anything).Return(nil).Once()
			},
			expectedError: nil,
		},
		{
			name: "Successful delete of param_group scope param",
			request: DeleteParamsRequest{
				UserID:  userID,
				ScopeID: groupID,
				Scope:   ParamGroupScope.String(),
				Keys:    []string{"minSpread"},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{Total: 1}, nil).Once()
				mockParamsRepo.On("Create", mock.Anything, mock.Anything).Return(nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, paramrepo.ListBotParamGroupsRelationsRequest{GroupID: groupID}).Return(&paramrepo.ListBotParamGroupsResponse{BotParamGroups: []paramrepo.BotParamGroup{{BotID: botID}}}, nil).Once()
				mockBotsRepo.On("Get", mock.Anything, botID, true).Return(&botrepo.Bot{ID: botID, Active: true, Account: accRepo.Account{RegionID: "region-1"}}, nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, mock.Anything).Return(&paramrepo.ListBotParamGroupsResponse{}, nil).Once()
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{}, nil).Once()
				mockParamsRepo.On("ListParamGroups", mock.Anything, mock.Anything).Return(&paramrepo.ListParamGroupsResponse{}, nil).Once()
				mockBotRegionalCli.On("UpdateBotParam", mock.Anything, "region-1", mock.Anything).Return(nil).Once()
			},
			expectedError: nil,
		},
		{
			name: "Params not found for scope",
			request: DeleteParamsRequest{
				UserID:  userID,
				ScopeID: botID,
				Scope:   BotScope.String(),
				Keys:    []string{"minSpread"},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{Total: 0}, nil).Once()
			},
			expectedError: errors.NotFound(codes.Params, "there are no parameters for this scope"),
		},
		{
			name: "Invalid request validation",
			request: DeleteParamsRequest{
				UserID: "invalid-user-id", // Not a UUID
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
			},
			expectedError: errors.Errorf("validation failed"), // Generic error for checking
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParamsRepo := paramrepo.NewIParamsMocked(t)
			mockBotsRepo := botrepo.NewIBotsMocked(t)
			mockAccountRepo := accRepo.NewIAccountsMocked(t)
			mockBotRegionalClient := botregionalclient.NewIBotRegionalClientMocked(t)

			if tt.setupMocks != nil {
				tt.setupMocks(mockBotsRepo, mockParamsRepo, mockBotRegionalClient)
			}

			paramSvc := New(mockBotsRepo, mockParamsRepo, mockAccountRepo, mockBotRegionalClient, "tooSecret", 1)

			err := paramSvc.Delete(context.Background(), tt.request)

			if tt.expectedError != nil {
				assert.Error(t, err)
				if tt.name == "Invalid request validation" {
					assert.Contains(t, err.Error(), "validation")
				} else {
					assert.Equal(t, tt.expectedError, err)
				}
			} else {
				assert.NoError(t, err)
			}

			mockBotsRepo.AssertExpectations(t)
			mockParamsRepo.AssertExpectations(t)
			mockBotRegionalClient.AssertExpectations(t)
		})
	}
}

func TestService_Create(t *testing.T) {
	userID := uuid.New().String()
	botID := uuid.New().String()
	accountID := uuid.New().String()
	groupID := uuid.New().String()

	tests := []struct {
		name             string
		request          CreateParamsRequest
		setupMocks       func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockAccountRepo *accRepo.IAccountsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked)
		expectedResponse *CreateParamsResponse
		expectedError    error
	}{
		{
			name: "Successful create of bot scope param",
			request: CreateParamsRequest{
				UserID:  userID,
				ScopeID: botID,
				Scope:   BotScope.String(),
				Params:  []ParamRequest{{Key: "minSpread", Value: "10"}},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockAccountRepo *accRepo.IAccountsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockBotsRepo.On("Get", mock.Anything, botID, true).Return(&botrepo.Bot{ID: botID, Active: true, Account: accRepo.Account{RegionID: "region-1"}}, nil).Twice()
				mockParamsRepo.On("Create", mock.Anything, mock.Anything).Return(nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, mock.Anything).Return(&paramrepo.ListBotParamGroupsResponse{}, nil).Once()
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{}, nil).Once()
				mockParamsRepo.On("ListParamGroups", mock.Anything, mock.Anything).Return(&paramrepo.ListParamGroupsResponse{}, nil).Once()
				mockBotRegionalCli.On("UpdateBotParam", mock.Anything, "region-1", mock.Anything).Return(nil).Once()
			},
			expectedResponse: &CreateParamsResponse{Params: []Param{{ScopeID: botID, Scope: BotScope.String(), Key: "minSpread", Value: "10", CreatedById: userID}}},
			expectedError:    nil,
		},
		{
			name: "Successful create of gateway scope param with encryption",
			request: CreateParamsRequest{
				UserID:  userID,
				ScopeID: accountID,
				Scope:   GatewayScope.String(),
				Params:  []ParamRequest{{Key: "api_secret", Value: "my-secret"}},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockAccountRepo *accRepo.IAccountsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockAccountRepo.On("Get", mock.Anything, accountID).Return(&accRepo.Account{ID: accountID, RegionID: "region-1"}, nil).Once()
				mockParamsRepo.On("Create", mock.Anything, mock.MatchedBy(func(params []paramrepo.Param) bool {
					return len(params) == 1 && params[0].Key == "api_secret" && params[0].IsEncrypted
				})).Return(nil).Once()
				mockBotsRepo.On("List", mock.Anything, mock.Anything).Return(&botrepo.ListBotsResponse{Bots: []*botrepo.Bot{{ID: botID, Active: true, Account: accRepo.Account{RegionID: "region-1"}}}}, nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, mock.Anything).Return(&paramrepo.ListBotParamGroupsResponse{}, nil).Once()
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{}, nil).Once()
				mockParamsRepo.On("ListParamGroups", mock.Anything, mock.Anything).Return(&paramrepo.ListParamGroupsResponse{}, nil).Once()
				mockBotRegionalCli.On("UpdateBotParam", mock.Anything, "region-1", mock.Anything).Return(nil).Once()
			},
			expectedResponse: &CreateParamsResponse{Params: []Param{{ScopeID: accountID, Scope: GatewayScope.String(), Key: "api_secret", Value: "my-secret", IsEncrypted: true, SecretKeyVersion: 1, CreatedById: userID}}},
			expectedError:    nil,
		},
		{
			name: "Successful create of param_group scope param",
			request: CreateParamsRequest{
				UserID:  userID,
				ScopeID: groupID,
				Scope:   ParamGroupScope.String(),
				Params:  []ParamRequest{{Key: "minSpread", Value: "50"}},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockAccountRepo *accRepo.IAccountsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockParamsRepo.On("GetParamGroup", mock.Anything, groupID).Return(&paramrepo.ParamGroup{ID: groupID}, nil).Once()
				mockParamsRepo.On("Create", mock.Anything, mock.Anything).Return(nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, paramrepo.ListBotParamGroupsRelationsRequest{GroupID: groupID}).Return(&paramrepo.ListBotParamGroupsResponse{BotParamGroups: []paramrepo.BotParamGroup{{BotID: botID}}}, nil).Once()
				mockBotsRepo.On("Get", mock.Anything, botID, true).Return(&botrepo.Bot{ID: botID, Active: true, Account: accRepo.Account{RegionID: "region-1"}}, nil).Once()
				mockParamsRepo.On("ListBotParamGroupsRelations", mock.Anything, mock.Anything).Return(&paramrepo.ListBotParamGroupsResponse{}, nil).Once()
				mockParamsRepo.On("List", mock.Anything, mock.Anything).Return(&paramrepo.ListParamsResponse{}, nil).Once()
				mockParamsRepo.On("ListParamGroups", mock.Anything, mock.Anything).Return(&paramrepo.ListParamGroupsResponse{}, nil).Once()
				mockBotRegionalCli.On("UpdateBotParam", mock.Anything, "region-1", mock.Anything).Return(nil).Once()
			},
			expectedResponse: &CreateParamsResponse{Params: []Param{{ScopeID: groupID, Scope: ParamGroupScope.String(), Key: "minSpread", Value: "50", CreatedById: userID}}},
			expectedError:    nil,
		},
		{
			name: "Bot not found",
			request: CreateParamsRequest{
				UserID:  userID,
				ScopeID: botID,
				Scope:   BotScope.String(),
				Params:  []ParamRequest{{Key: "minSpread", Value: "10"}},
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockAccountRepo *accRepo.IAccountsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
				mockBotsRepo.On("Get", mock.Anything, botID, true).Return(nil, errors.NotFound(codes.Bots, "bot not found")).Once()
			},
			expectedResponse: nil,
			expectedError:    errors.NotFound(codes.Bots, "bot not found"),
		},
		{
			name: "Invalid request validation",
			request: CreateParamsRequest{
				UserID: "invalid-user-id",
			},
			setupMocks: func(mockBotsRepo *botrepo.IBotsMocked, mockParamsRepo *paramrepo.IParamsMocked, mockAccountRepo *accRepo.IAccountsMocked, mockBotRegionalCli *botregionalclient.IBotRegionalClientMocked) {
			},
			expectedResponse: nil,
			expectedError:    errors.Errorf("validation failed"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParamsRepo := paramrepo.NewIParamsMocked(t)
			mockBotsRepo := botrepo.NewIBotsMocked(t)
			mockAccountRepo := accRepo.NewIAccountsMocked(t)
			mockBotRegionalClient := botregionalclient.NewIBotRegionalClientMocked(t)

			if tt.setupMocks != nil {
				tt.setupMocks(mockBotsRepo, mockParamsRepo, mockAccountRepo, mockBotRegionalClient)
			}

			paramSvc := New(mockBotsRepo, mockParamsRepo, mockAccountRepo, mockBotRegionalClient, "mZAPi62R2rS+n5pG4t49h4pT1uI04xT7wVzV8t6L8bA=", 1)

			result, err := paramSvc.Create(context.Background(), tt.request)

			if tt.expectedError != nil {
				assert.Error(t, err)
				if tt.name == "Invalid request validation" {
					assert.Contains(t, err.Error(), "validation")
				} else {
					assert.Equal(t, tt.expectedError.Error(), err.Error())
				}
			} else {
				assert.NoError(t, err)
				// Ignore time for comparison
				for i := range result.Params {
					result.Params[i].CreatedAt = time.Time{}
				}
				assert.Equal(t, tt.expectedResponse, result)
			}

			mockBotsRepo.AssertExpectations(t)
			mockParamsRepo.AssertExpectations(t)
			mockAccountRepo.AssertExpectations(t)
			mockBotRegionalClient.AssertExpectations(t)
		})
	}
}
