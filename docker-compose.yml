# Top-level 'secrets' definition
secrets:
  # This defines a secret named 'ssh<PERSON><PERSON>' that the 'botmanager' service can use.
  sshKey:
    # It points to the source file on your local machine.
    file: ${HOME}/.ssh/github_ed25519

volumes:
  psql:
  redis_data:

networks:
  network:
    attachable: true
    driver: bridge

services:
  botmanager:
    image: botmanager:dev
    container_name: botmanager
    networks:
      - network
    build:
      secrets:
        - sshKey
      context: .
      dockerfile: ./Dockerfile
      target: dev
    volumes:
      - .:/app/
      - ${HOME}/.ssh/github_ed25519:/root/.ssh/github_ed25519
      - ${HOME}/.ssh/config:/root/.ssh/config
    command: >
      sh -c "
        chown root:root /root/.ssh/config /root/.ssh/github_ed25519 && \
        chmod 600 /root/.ssh/config /root/.ssh/github_ed25519 && \
        air -c .air.toml
      "
    ports:
      - "8000:8000"
     # delve debug port
      - "40000:40000"
    restart: unless-stopped
    secrets:
      - sshKey
    environment:
      - DATABASE_DSN=**************************************/botmanager?sslmode=disable
      - REDIS_ADDR=redis:6379
      - LISTEN_ADDR=0.0.0.0:8000
    depends_on:
      psql:
        condition: service_healthy
      redis:
        condition: service_started

  psql:
    image: bitnami/postgresql:16
    container_name: psql
    networks:
      - network
    user: root
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 10s
      retries: 10
    environment:
      - POSTGRES_MAX_CONNECTIONS=10
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=botmanager
    ports:
      - "5432:5432"
    volumes:
      - psql:/bitnami/postgresql/data

  redis:
    image: bitnami/redis:7.2
    container_name: redis
    networks:
      - network
    user: root
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    networks:
      - network
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  cronjob:
    image: hibiken/asynqmon:latest
    container_name: cronjob
    ports:
      - "3000:3000"
    networks:
      - network
    depends_on:
      - redis
      - prometheus
    command: --port=3000 --prometheus-addr=http://localhost:9090 --enable-metrics-exporter --redis-addr=redis:6379
