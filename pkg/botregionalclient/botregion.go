package botregionalclient

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/herenow/atomic-bm/internal/app"
	"github.com/herenow/atomic-protocols/gen/atomic/api/botregion/v1"
	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"github.com/herenow/atomic-protocols/rpc"
	"github.com/sony/gobreaker/v2"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/structpb"
)

// IBotRegionalClient defines the interface for the bot regional client.
type IBotRegionalClient interface {
	NewBot(ctx context.Context, regionID string, bot *proto.Bot) error
	ListBots(ctx context.Context, regionID string) ([]*proto.Bot, error)
	GetBot(ctx context.Context, regionID string, botID string) (*proto.Bot, error)
	GetBotState(ctx context.Context, regionID string, botID string) (*proto.State, error)
	StartBot(ctx context.Context, regionID string, botID string) error
	StopBot(ctx context.Context, regionID string, botID string) error
	UpdateBotParams(ctx context.Context, regionID string, req []BotParams) error
	UpdateGtwOpts(ctx context.Context, regionID string, req []GtwOpts) error
	StopAllBots(ctx context.Context, regionID string) error
	StartAllBots(ctx context.Context, regionID string) error
	ListBotsState(ctx context.Context, regionID string) ([]*proto.State, error)
}

type regionDetails struct {
	address  string
	insecure bool
	apiKey   string
}

type Client struct {
	regionDetails map[string]regionDetails
	clients       map[string]botregion.BotRegionServiceClient
	breakers      map[string]*gobreaker.CircuitBreaker[any]
	mu            sync.RWMutex
}

// New creates a pool of botregion clients it receives a map which expects the key
// to be the regionID and the value to be the actual address to the server.
func New(config *app.Config) (IBotRegionalClient, error) {
	detailsMap := make(map[string]regionDetails)
	for _, conf := range config.BotRegionalServers {
		if conf.RegionID == "" || conf.BotRegionalAddr == "" {
			return nil, fmt.Errorf("region_id and bot_regional_addr are required fields in config")
		}
		detailsMap[conf.RegionID] = regionDetails{
			address:  conf.BotRegionalAddr,
			insecure: conf.Insecure,
			apiKey:   conf.ApiKey,
		}
	}

	c := &Client{
		regionDetails: detailsMap,
		clients:       make(map[string]botregion.BotRegionServiceClient),
		breakers:      make(map[string]*gobreaker.CircuitBreaker[any]),
	}
	return c, nil
}

func (c *Client) getContextWithAuth(ctx context.Context, regionID string) (context.Context, error) {
	c.mu.RLock()
	details, ok := c.regionDetails[regionID]
	c.mu.RUnlock()

	if !ok {
		// This case is already handled by resolveClient, but it's good practice.
		return nil, fmt.Errorf("configuration for region ID '%s' not found", regionID)
	}

	if details.apiKey == "" {
		// If a key is expected but not provided in the config, return an error.
		return nil, fmt.Errorf("API key for region '%s' is empty", regionID)
	}

	// Create gRPC metadata with the x-api-key header.
	md := metadata.New(map[string]string{"x-api-key": details.apiKey})
	// Append the metadata to the outgoing context.
	return metadata.NewOutgoingContext(ctx, md), nil
}

func (c *Client) resolveClient(regionID string) (botregion.BotRegionServiceClient, *gobreaker.CircuitBreaker[any], error) {
	c.mu.RLock()
	client, ok := c.clients[regionID]
	breaker, breakerOk := c.breakers[regionID]
	c.mu.RUnlock()

	if ok && breakerOk {
		return client, breaker, nil
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	client, ok = c.clients[regionID]
	breaker, breakerOk = c.breakers[regionID]

	if ok && breakerOk {
		return client, breaker, nil
	}

	details, ok := c.regionDetails[regionID]
	if !ok {
		return nil, nil, fmt.Errorf("invalid region ID: %s", regionID)
	}

	conn, err := rpc.NewClient(details.address, details.insecure, nil, nil, nil)
	if err != nil {
		return nil, nil, err
	}

	client = botregion.NewBotRegionServiceClient(conn)
	c.clients[regionID] = client

	var st gobreaker.Settings
	st.Name = fmt.Sprintf("gRPC BotRegionalClient %s", regionID)
	st.Timeout = 60 * time.Second
	st.ReadyToTrip = func(counts gobreaker.Counts) bool {
		failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
		return counts.Requests >= 3 && failureRatio >= 0.6
	}

	c.breakers[regionID] = gobreaker.NewCircuitBreaker[any](st)

	return client, c.breakers[regionID], nil
}

func (c *Client) NewBot(ctx context.Context, regionID string, bot *proto.Bot) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	_, err = cb.Execute(func() (any, error) {
		return api.NewBot(authedCtx, &botregion.NewBotRequest{Bot: bot})
	})
	return err
}

func (c *Client) GetBot(ctx context.Context, regionID string, botID string) (*proto.Bot, error) {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return nil, err
	}

	res, err := cb.Execute(func() (any, error) {
		return api.GetBot(authedCtx, &botregion.GetBotRequest{BotId: botID})
	})
	if err != nil {
		return nil, err
	}

	return res.(*botregion.GetBotResponse).GetBot(), nil
}

func (c *Client) StartBot(ctx context.Context, regionID string, botID string) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	_, err = cb.Execute(func() (any, error) {
		return api.StartBot(authedCtx, &botregion.StartBotRequest{BotId: botID})

	})
	return err
}

func (c *Client) StopBot(ctx context.Context, regionID string, botID string) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	_, err = cb.Execute(func() (any, error) {
		return api.StopBot(authedCtx, &botregion.StopBotRequest{BotId: botID})
	})
	return err
}

func (c *Client) ListBots(ctx context.Context, regionID string) ([]*proto.Bot, error) {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return nil, err
	}

	res, err := cb.Execute(func() (any, error) {
		return api.ListBots(authedCtx, nil)
	})
	if err != nil {
		return nil, err
	}
	return res.(*botregion.ListBotsResponse).GetBots(), nil
}

func (c *Client) GetBotState(ctx context.Context, regionID string, botID string) (*proto.State, error) {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}
	// CHANGED: Get authenticated context
	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return nil, err
	}

	res, err := cb.Execute(func() (any, error) {
		return api.GetBotState(authedCtx, &botregion.GetBotStateRequest{BotId: botID})
	})
	if err != nil {
		return nil, err
	}
	return res.(*botregion.GetBotStateResponse).GetState(), nil
}

type BotParams struct {
	BotID  string
	Params *structpb.Struct
}

func (c *Client) UpdateBotParams(ctx context.Context, regionID string, req []BotParams) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	var params []*botregion.BotParamsUpdate
	for _, param := range req {
		params = append(params, &botregion.BotParamsUpdate{
			BotId:  param.BotID,
			Params: param.Params,
		})
	}

	_, err = cb.Execute(func() (any, error) {
		return api.UpdateBotParams(authedCtx, &botregion.UpdateBotParamsRequest{
			BotUpdates: params,
		})
	})
	return err
}

func (c *Client) StartAllBots(ctx context.Context, regionID string) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}
	// CHANGED: Get authenticated context
	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	_, err = cb.Execute(func() (any, error) {
		return api.StartAllBots(authedCtx, nil)
	})
	return err
}

func (c *Client) StopAllBots(ctx context.Context, regionID string) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	_, err = cb.Execute(func() (any, error) {
		return api.StopAllBots(authedCtx, nil)
	})
	return err
}

type GtwOpts struct {
	BotID   string
	GtwOpts *structpb.Struct
}

func (c *Client) UpdateGtwOpts(ctx context.Context, regionID string, req []GtwOpts) error {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return err
	}

	var gtwOpts []*botregion.UpdateGatewayOptions
	for _, param := range req {
		gtwOpts = append(gtwOpts, &botregion.UpdateGatewayOptions{
			BotId:      param.BotID,
			GtwOptions: param.GtwOpts,
		})
	}

	_, err = cb.Execute(func() (any, error) {
		return api.UpdateGatewayOpts(authedCtx, &botregion.UpdateGatewayOptionsRequest{
			GtwUpdates: gtwOpts,
		})
	})

	return err
}

func (c *Client) ListBotsState(ctx context.Context, regionID string) ([]*proto.State, error) {
	api, cb, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}

	authedCtx, err := c.getContextWithAuth(ctx, regionID)
	if err != nil {
		return nil, err
	}

	res, err := cb.Execute(func() (any, error) {
		return api.ListBotsState(authedCtx, nil)
	})
	if err != nil {
		return nil, err
	}
	return res.(*botregion.ListBotsStateResponse).GetBotsState(), nil
}
