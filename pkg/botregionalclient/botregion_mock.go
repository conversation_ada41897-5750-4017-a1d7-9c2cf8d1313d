// Code generated by mockery. DO NOT EDIT.

package botregionalclient

import (
	context "context"

	proto "github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	mock "github.com/stretchr/testify/mock"
)

// IBotRegionalClientMocked is an autogenerated mock type for the IBotRegionalClient type
type IBotRegionalClientMocked struct {
	mock.Mock
}

type IBotRegionalClientMocked_Expecter struct {
	mock *mock.Mock
}

func (_m *IBotRegionalClientMocked) EXPECT() *IBotRegionalClientMocked_Expecter {
	return &IBotRegionalClientMocked_Expecter{mock: &_m.Mock}
}

// GetBot provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) GetBot(ctx context.Context, regionID string, botID string) (*proto.Bot, error) {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for GetBot")
	}

	var r0 *proto.Bot
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*proto.Bot, error)); ok {
		return rf(ctx, regionID, botID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *proto.Bot); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*proto.Bot)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, regionID, botID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotRegionalClientMocked_GetBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBot'
type IBotRegionalClientMocked_GetBot_Call struct {
	*mock.Call
}

// GetBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) GetBot(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_GetBot_Call {
	return &IBotRegionalClientMocked_GetBot_Call{Call: _e.mock.On("GetBot", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_GetBot_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_GetBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_GetBot_Call) Return(_a0 *proto.Bot, _a1 error) *IBotRegionalClientMocked_GetBot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotRegionalClientMocked_GetBot_Call) RunAndReturn(run func(context.Context, string, string) (*proto.Bot, error)) *IBotRegionalClientMocked_GetBot_Call {
	_c.Call.Return(run)
	return _c
}

// GetBotState provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) GetBotState(ctx context.Context, regionID string, botID string) (*proto.State, error) {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for GetBotState")
	}

	var r0 *proto.State
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*proto.State, error)); ok {
		return rf(ctx, regionID, botID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *proto.State); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*proto.State)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, regionID, botID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotRegionalClientMocked_GetBotState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBotState'
type IBotRegionalClientMocked_GetBotState_Call struct {
	*mock.Call
}

// GetBotState is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) GetBotState(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_GetBotState_Call {
	return &IBotRegionalClientMocked_GetBotState_Call{Call: _e.mock.On("GetBotState", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_GetBotState_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_GetBotState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_GetBotState_Call) Return(_a0 *proto.State, _a1 error) *IBotRegionalClientMocked_GetBotState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotRegionalClientMocked_GetBotState_Call) RunAndReturn(run func(context.Context, string, string) (*proto.State, error)) *IBotRegionalClientMocked_GetBotState_Call {
	_c.Call.Return(run)
	return _c
}

// ListBots provides a mock function with given fields: ctx, regionID
func (_m *IBotRegionalClientMocked) ListBots(ctx context.Context, regionID string) ([]*proto.Bot, error) {
	ret := _m.Called(ctx, regionID)

	if len(ret) == 0 {
		panic("no return value specified for ListBots")
	}

	var r0 []*proto.Bot
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*proto.Bot, error)); ok {
		return rf(ctx, regionID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*proto.Bot); ok {
		r0 = rf(ctx, regionID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*proto.Bot)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, regionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotRegionalClientMocked_ListBots_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBots'
type IBotRegionalClientMocked_ListBots_Call struct {
	*mock.Call
}

// ListBots is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
func (_e *IBotRegionalClientMocked_Expecter) ListBots(ctx interface{}, regionID interface{}) *IBotRegionalClientMocked_ListBots_Call {
	return &IBotRegionalClientMocked_ListBots_Call{Call: _e.mock.On("ListBots", ctx, regionID)}
}

func (_c *IBotRegionalClientMocked_ListBots_Call) Run(run func(ctx context.Context, regionID string)) *IBotRegionalClientMocked_ListBots_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_ListBots_Call) Return(_a0 []*proto.Bot, _a1 error) *IBotRegionalClientMocked_ListBots_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotRegionalClientMocked_ListBots_Call) RunAndReturn(run func(context.Context, string) ([]*proto.Bot, error)) *IBotRegionalClientMocked_ListBots_Call {
	_c.Call.Return(run)
	return _c
}

// ListBotsState provides a mock function with given fields: ctx, regionID
func (_m *IBotRegionalClientMocked) ListBotsState(ctx context.Context, regionID string) ([]*proto.State, error) {
	ret := _m.Called(ctx, regionID)

	if len(ret) == 0 {
		panic("no return value specified for ListBotsState")
	}

	var r0 []*proto.State
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*proto.State, error)); ok {
		return rf(ctx, regionID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*proto.State); ok {
		r0 = rf(ctx, regionID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*proto.State)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, regionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotRegionalClientMocked_ListBotsState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBotsState'
type IBotRegionalClientMocked_ListBotsState_Call struct {
	*mock.Call
}

// ListBotsState is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
func (_e *IBotRegionalClientMocked_Expecter) ListBotsState(ctx interface{}, regionID interface{}) *IBotRegionalClientMocked_ListBotsState_Call {
	return &IBotRegionalClientMocked_ListBotsState_Call{Call: _e.mock.On("ListBotsState", ctx, regionID)}
}

func (_c *IBotRegionalClientMocked_ListBotsState_Call) Run(run func(ctx context.Context, regionID string)) *IBotRegionalClientMocked_ListBotsState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_ListBotsState_Call) Return(_a0 []*proto.State, _a1 error) *IBotRegionalClientMocked_ListBotsState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotRegionalClientMocked_ListBotsState_Call) RunAndReturn(run func(context.Context, string) ([]*proto.State, error)) *IBotRegionalClientMocked_ListBotsState_Call {
	_c.Call.Return(run)
	return _c
}

// NewBot provides a mock function with given fields: ctx, regionID, bot
func (_m *IBotRegionalClientMocked) NewBot(ctx context.Context, regionID string, bot *proto.Bot) error {
	ret := _m.Called(ctx, regionID, bot)

	if len(ret) == 0 {
		panic("no return value specified for NewBot")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *proto.Bot) error); ok {
		r0 = rf(ctx, regionID, bot)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_NewBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewBot'
type IBotRegionalClientMocked_NewBot_Call struct {
	*mock.Call
}

// NewBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - bot *proto.Bot
func (_e *IBotRegionalClientMocked_Expecter) NewBot(ctx interface{}, regionID interface{}, bot interface{}) *IBotRegionalClientMocked_NewBot_Call {
	return &IBotRegionalClientMocked_NewBot_Call{Call: _e.mock.On("NewBot", ctx, regionID, bot)}
}

func (_c *IBotRegionalClientMocked_NewBot_Call) Run(run func(ctx context.Context, regionID string, bot *proto.Bot)) *IBotRegionalClientMocked_NewBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*proto.Bot))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_NewBot_Call) Return(_a0 error) *IBotRegionalClientMocked_NewBot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_NewBot_Call) RunAndReturn(run func(context.Context, string, *proto.Bot) error) *IBotRegionalClientMocked_NewBot_Call {
	_c.Call.Return(run)
	return _c
}

// StartAllBots provides a mock function with given fields: ctx, regionID
func (_m *IBotRegionalClientMocked) StartAllBots(ctx context.Context, regionID string) error {
	ret := _m.Called(ctx, regionID)

	if len(ret) == 0 {
		panic("no return value specified for StartAllBots")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, regionID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_StartAllBots_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartAllBots'
type IBotRegionalClientMocked_StartAllBots_Call struct {
	*mock.Call
}

// StartAllBots is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
func (_e *IBotRegionalClientMocked_Expecter) StartAllBots(ctx interface{}, regionID interface{}) *IBotRegionalClientMocked_StartAllBots_Call {
	return &IBotRegionalClientMocked_StartAllBots_Call{Call: _e.mock.On("StartAllBots", ctx, regionID)}
}

func (_c *IBotRegionalClientMocked_StartAllBots_Call) Run(run func(ctx context.Context, regionID string)) *IBotRegionalClientMocked_StartAllBots_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_StartAllBots_Call) Return(_a0 error) *IBotRegionalClientMocked_StartAllBots_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_StartAllBots_Call) RunAndReturn(run func(context.Context, string) error) *IBotRegionalClientMocked_StartAllBots_Call {
	_c.Call.Return(run)
	return _c
}

// StartBot provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) StartBot(ctx context.Context, regionID string, botID string) error {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for StartBot")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_StartBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartBot'
type IBotRegionalClientMocked_StartBot_Call struct {
	*mock.Call
}

// StartBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) StartBot(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_StartBot_Call {
	return &IBotRegionalClientMocked_StartBot_Call{Call: _e.mock.On("StartBot", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_StartBot_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_StartBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_StartBot_Call) Return(_a0 error) *IBotRegionalClientMocked_StartBot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_StartBot_Call) RunAndReturn(run func(context.Context, string, string) error) *IBotRegionalClientMocked_StartBot_Call {
	_c.Call.Return(run)
	return _c
}

// StopAllBots provides a mock function with given fields: ctx, regionID
func (_m *IBotRegionalClientMocked) StopAllBots(ctx context.Context, regionID string) error {
	ret := _m.Called(ctx, regionID)

	if len(ret) == 0 {
		panic("no return value specified for StopAllBots")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, regionID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_StopAllBots_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopAllBots'
type IBotRegionalClientMocked_StopAllBots_Call struct {
	*mock.Call
}

// StopAllBots is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
func (_e *IBotRegionalClientMocked_Expecter) StopAllBots(ctx interface{}, regionID interface{}) *IBotRegionalClientMocked_StopAllBots_Call {
	return &IBotRegionalClientMocked_StopAllBots_Call{Call: _e.mock.On("StopAllBots", ctx, regionID)}
}

func (_c *IBotRegionalClientMocked_StopAllBots_Call) Run(run func(ctx context.Context, regionID string)) *IBotRegionalClientMocked_StopAllBots_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_StopAllBots_Call) Return(_a0 error) *IBotRegionalClientMocked_StopAllBots_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_StopAllBots_Call) RunAndReturn(run func(context.Context, string) error) *IBotRegionalClientMocked_StopAllBots_Call {
	_c.Call.Return(run)
	return _c
}

// StopBot provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) StopBot(ctx context.Context, regionID string, botID string) error {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for StopBot")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_StopBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopBot'
type IBotRegionalClientMocked_StopBot_Call struct {
	*mock.Call
}

// StopBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) StopBot(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_StopBot_Call {
	return &IBotRegionalClientMocked_StopBot_Call{Call: _e.mock.On("StopBot", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_StopBot_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_StopBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_StopBot_Call) Return(_a0 error) *IBotRegionalClientMocked_StopBot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_StopBot_Call) RunAndReturn(run func(context.Context, string, string) error) *IBotRegionalClientMocked_StopBot_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBotParams provides a mock function with given fields: ctx, regionID, req
func (_m *IBotRegionalClientMocked) UpdateBotParams(ctx context.Context, regionID string, req []BotParams) error {
	ret := _m.Called(ctx, regionID, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBotParams")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []BotParams) error); ok {
		r0 = rf(ctx, regionID, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_UpdateBotParams_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBotParams'
type IBotRegionalClientMocked_UpdateBotParams_Call struct {
	*mock.Call
}

// UpdateBotParams is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - req []BotParams
func (_e *IBotRegionalClientMocked_Expecter) UpdateBotParams(ctx interface{}, regionID interface{}, req interface{}) *IBotRegionalClientMocked_UpdateBotParams_Call {
	return &IBotRegionalClientMocked_UpdateBotParams_Call{Call: _e.mock.On("UpdateBotParams", ctx, regionID, req)}
}

func (_c *IBotRegionalClientMocked_UpdateBotParams_Call) Run(run func(ctx context.Context, regionID string, req []BotParams)) *IBotRegionalClientMocked_UpdateBotParams_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]BotParams))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_UpdateBotParams_Call) Return(_a0 error) *IBotRegionalClientMocked_UpdateBotParams_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_UpdateBotParams_Call) RunAndReturn(run func(context.Context, string, []BotParams) error) *IBotRegionalClientMocked_UpdateBotParams_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateGtwOpts provides a mock function with given fields: ctx, regionID, req
func (_m *IBotRegionalClientMocked) UpdateGtwOpts(ctx context.Context, regionID string, req []GtwOpts) error {
	ret := _m.Called(ctx, regionID, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateGtwOpts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []GtwOpts) error); ok {
		r0 = rf(ctx, regionID, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_UpdateGtwOpts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateGtwOpts'
type IBotRegionalClientMocked_UpdateGtwOpts_Call struct {
	*mock.Call
}

// UpdateGtwOpts is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - req []GtwOpts
func (_e *IBotRegionalClientMocked_Expecter) UpdateGtwOpts(ctx interface{}, regionID interface{}, req interface{}) *IBotRegionalClientMocked_UpdateGtwOpts_Call {
	return &IBotRegionalClientMocked_UpdateGtwOpts_Call{Call: _e.mock.On("UpdateGtwOpts", ctx, regionID, req)}
}

func (_c *IBotRegionalClientMocked_UpdateGtwOpts_Call) Run(run func(ctx context.Context, regionID string, req []GtwOpts)) *IBotRegionalClientMocked_UpdateGtwOpts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]GtwOpts))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_UpdateGtwOpts_Call) Return(_a0 error) *IBotRegionalClientMocked_UpdateGtwOpts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_UpdateGtwOpts_Call) RunAndReturn(run func(context.Context, string, []GtwOpts) error) *IBotRegionalClientMocked_UpdateGtwOpts_Call {
	_c.Call.Return(run)
	return _c
}

// NewIBotRegionalClientMocked creates a new instance of IBotRegionalClientMocked. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIBotRegionalClientMocked(t interface {
	mock.TestingT
	Cleanup(func())
}) *IBotRegionalClientMocked {
	mock := &IBotRegionalClientMocked{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
