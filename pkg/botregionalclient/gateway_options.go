package botregionalclient

import (
	"net/url"
	"time"
)

// Options represents the configuration options for a gateway
type Options struct {
	// Auth
	ApiKey        string `long:"apiKey" description:"API key for authentication" json:"apiKey,omitempty"`
	ApiSecret     string `long:"apiSecret" description:"API secret for authentication" json:"apiSecret,omitempty"`
	ApiMemo       string `long:"apiMemo" description:"API memo used by some exchanges for authentication" json:"apiMemo,omitempty"`
	ApiPassphrase string `long:"apiPassphrase" description:"API passphrase for authentication" json:"apiPassphrase,omitempty"`
	ApiVersion    string `long:"apiVersion" description:"API version to use when making requests to the exchange" json:"apiVersion,omitempty"`
	Token         string `long:"token" description:"API token for authentication" json:"token,omitempty"`
	Cookie        string `long:"cookie" description:"Cookie to use when making http requests to the exchange" json:"cookie,omitempty"`
	UserAgent     string `long:"userAgent" description:"User agent to use when making http requests to the exchange" json:"userAgent,omitempty"`
	// User/account id on the exchange
	UserID string `long:"userID" description:"Manually specify the userID of the user on the exchange" json:"userID,omitempty"`
	// Connect to staging environment
	Staging bool `long:"staging" description:"Connect to staging environment" json:"staging,omitempty"`
	// Refresh interval is an option that some gateways might use
	// when quotes (orderbook) is updated manually, via pooling.
	// When websocket intergration might not be available.
	RefreshIntervalMs int `long:"refreshIntervalMs" description:"How long to poll for updates on gtws that only support polling" json:"refreshIntervalMs,omitempty"`
	// Force market data polling
	PollMarketData bool `long:"pollMarketData" description:"This will force market data polling when available instead of using the websocket endpoint" json:"pollMarketData,omitempty"`
	// Force market data polling
	PollAccountData bool `long:"pollAccountData" description:"This will force account data polling when available instead of using the websocket endpoint" json:"pollAccountData,omitempty"`
	// Use by CurrencyLayer gateway to define the base symbol
	// when building FX quotes
	FxSource   string   `long:"fxSource" description:"FastForex and Currency layer specific option to define the base symbol" json:"fxSource,omitempty"`
	LoadMarket []string `long:"loadMarket" description:"Some exchanges, such as DEXs, we might need to manually load the desired market" json:"loadMarket,omitempty"`
	// Proxy servers to use
	Proxies []*url.URL `json:"-" description:"Proxy servers to use (protocol://username:password@host:port,...)"`
	// Define custom address to connect to
	WsAddr string `long:"wsAddr" description:"Custom websocket addr to connect to" json:"wsAddr,omitempty"`
	WsHost string `long:"wsHost" description:"Custom host header to use when connecting to ws" json:"wsHost,omitempty"`
	// Verbose mode
	Verbose bool `long:"verbose" description:"Verbose logging mode" json:"verbose,omitempty"`
	// Some exchanges have async order entries, the rest api will return an orderID, but the order has been
	// queued for entry on the orderbook, this will force our gtw to wait for the order to be confirmed as open
	WaitForOrderEntry    bool          `long:"waitForOrderEntry" description:"Wait for order to be confirmed as open" json:"waitForOrderEntry,omitempty"`
	MaxWaitForOrderEntry time.Duration `long:"maxWaitForOrderEntry" description:"Max time to wait for order to be confirmed as open" json:"maxWaitForOrderEntry,omitempty"`
	// Broker ID is used by some exchanges, such as OKX, to identify the broker
	// that will receive fee rebates
	BrokerID string `long:"brokerID" description:"Broker ID used by some exchanges, to identify the broker that will receive fee rebates" json:"brokerID,omitempty"`
	// Use AWS endpoints if available, exchanges such as OKX, provide special endpoints w/
	// better latency when connecting from AWS
	UseAWSEndpoints bool `long:"useAWSEndpoints" description:"Use AWS endpoints if available" json:"useAWSEndpoints,omitempty"`
	// Option to authenticate all requests, even public ones
	// This is used by some exchanges, such as Bitso, which has higher rate limits per account basis
	// even on public endpoints
	AuthAllRequests bool `long:"authAllRequests" description:"Authenticate all requests, even public ones" json:"authAllRequests,omitempty"`
	// No proxies for private requests, useful for exchanges such as Bitso, that authenticated requests
	// have higher limits, and we have a direct link connection to the exchange, where we don't want to use proxies
	NoProxiesForPrivateRequests bool `long:"noProxiesForPrivateRequests" description:"No proxies for private requests" json:"noProxiesForPrivateRequests,omitempty"`
	// OriginSource is used by some exchanges, such as Trubit, to identify the origin of the request.
	// In the case of Trubit, it's to identify the MM that is sending the request
	OriginSource string `long:"originSource" description:"Origin source used by some exchanges to identify the origin of the request" json:"originSource,omitempty"`
	// Use custom base URL for API requests, useful e.g. for Digitra, that provides us with a custom endpoint
	APIBaseURL       string `long:"apiBaseURL" description:"Use custom endpoint for API requests" json:"apiBaseURL,omitempty"`
	WSBaseURL        string `long:"wsBaseURL" description:"Use custom endpoint for WebSocket requests" json:"wsBaseURL,omitempty"`
	AccountWSBaseURL string `long:"accountWsBaseURL" description:"Use custom endpoint for Account WebSocket requests" json:"accountWsBaseURL,omitempty"`
	// Poll for order updates instead of fills, useful for exchanges with account trades polling
	PollOrderUpdates bool `long:"pollOrderUpdates" description:"Poll for order updates instead of account trades" json:"pollOrderUpdates,omitempty"`
	// DEX specific options
	PoolAddresses []string `long:"poolAddresses" description:"List of pool addresses to use for UniswapV3" json:"poolAddresses,omitempty"`

	// --- Binance Specific Options ---
	// UseBinanceSBE enables the high-performance SBE binary protocol for market data.
	// This requires an Ed25519 API Key.
	UseBinanceSBE bool `long:"useBinanceSBE" description:"Use Binance SBE protocol for market data" json:"useBinanceSBE,omitempty"`

	// --- FIX Specific Options ---
	UseFIX          bool   `long:"useFIX" description:"Use FIX protocol for trading and market data"`
	FIXSenderCompID string `long:"fixSenderCompID" description:"FIX SenderCompID"`
	FIXTargetCompID string `long:"fixTargetCompID" description:"FIX TargetCompID"`
	FIXHost         string `long:"fixHost" description:"FIX connection host"`
	FIXPort         int    `long:"fixPort" description:"FIX connection port"`
}
