package botregionalclient

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"
)

// Test Structures from original file
type Simple struct {
	Name string `json:"name"`
	Age  int    `json:"age"`
}

type Nested struct {
	ID     string `json:"id"`
	Simple Simple `json:"simple"`
}

type Complex struct {
	Items   []string          `json:"items"`
	Labels  map[string]string `json:"labels"`
	Numbers []int             `json:"numbers"`
}

type WithPointer struct {
	Value *string `json:"value"`
}

type Unmarshallable struct {
	Data chan int
}

// runRoundtripTest is a generic helper to test the FromGoStruct and ToGoStruct functions.
func runRoundtripTest[T any](t *testing.T, name string, input T) {
	t.Run(name, func(t *testing.T) {
		// 1. Convert Go struct to proto.Struct
		protoStruct, err := FromGoStruct(input)
		require.NoError(t, err, "FromGoStruct returned an unexpected error")

		// 2. Convert proto.Struct back to Go struct
		result, err := ToGoStruct[T](protoStruct)
		require.NoError(t, err, "ToGoStruct returned an unexpected error")

		// 3. Assert that the result matches the input
		assert.Equal(t, input, result)
	})
}

func TestFromGoStruct_And_ToGoStruct_Roundtrip(t *testing.T) {
	strPtr := "hello"

	// Test cases for structs and pointers to structs
	runRoundtripTest(t, "Simple Struct", Simple{Name: "Alice", Age: 30})
	runRoundtripTest(t, "Pointer to Simple Struct", &Simple{Name: "Charlie", Age: 25})
	runRoundtripTest(t, "Nested Struct", Nested{ID: "xyz-123", Simple: Simple{Name: "Bob", Age: 45}})
	runRoundtripTest(t, "Complex Struct", Complex{
		Items:   []string{"one", "two", "three"},
		Labels:  map[string]string{"env": "prod", "version": "v1"},
		Numbers: []int{10, 20, 30},
	})
	runRoundtripTest(t, "Struct with Pointer Field (Not Nil)", WithPointer{Value: &strPtr})
	runRoundtripTest(t, "Struct with Pointer Field (Nil)", WithPointer{Value: nil})
	runRoundtripTest(t, "Empty Struct", Simple{})

	// Test cases for maps
	runRoundtripTest(t, "Map String String", map[string]string{"key1": "value1", "key2": "value2"})

	t.Run("Map String Interface", func(t *testing.T) {
		input := map[string]interface{}{
			"string": "a-string",
			"number": 123.45,
			"bool":   true,
			"nested": map[string]interface{}{"key": "val"},
		}
		protoStruct, err := FromGoStruct(input)
		require.NoError(t, err)
		result, err := ToGoStruct[map[string]interface{}](protoStruct)
		require.NoError(t, err)
		assert.EqualValues(t, input, result)
	})

	t.Run("Nil Input", func(t *testing.T) {
		protoStruct, err := FromGoStruct(nil)
		require.NoError(t, err)
		require.Nil(t, protoStruct, "FromGoStruct with nil input should return nil")

		result, err := ToGoStruct[Simple](nil)
		require.NoError(t, err)
		assert.Equal(t, Simple{}, result)
	})
}

// UPDATED TEST for gateway.Options using the enhanced ToGoStruct
func TestToGoStruct_GatewayOptions_From_StringMap(t *testing.T) {
	t.Run("Gateway Options from map[string]string (Botmanager scenario)", func(t *testing.T) {
		// 1. Simulate the data coming from Botmanager as a map[string]string.
		inputMap := map[string]string{
			"apiKey":            "key-from-db",
			"staging":           "true",
			"refreshIntervalMs": "5000",
			// The string value for proxies can be a JSON array or just comma-separated.
			"proxies":              `["http://proxy1.com","https://user:<EMAIL>"]`,
			"maxWaitForOrderEntry": "5s",
			"loadMarket":           "BTC/USD,ETH/USD",
		}

		// 2. Convert the map[string]string to a proto.Struct.
		protoStruct, err := FromGoStruct(inputMap)
		require.NoError(t, err)

		// 3. Convert the proto.Struct to gateway.Options using the new, robust ToGoStruct.
		// THIS STEP NOW SUCCEEDS.
		opts, err := ToGoStruct[Options](protoStruct)

		// 4. Assert that NO error occurred.
		require.NoError(t, err, "The enhanced ToGoStruct should handle the conversion without errors")

		// 5. Assert that the fields were parsed correctly.
		assert.Equal(t, "key-from-db", opts.ApiKey)
		assert.Equal(t, true, opts.Staging)
		assert.Equal(t, 5000, opts.RefreshIntervalMs)
		expectedDuration, _ := time.ParseDuration("5s")
		assert.Equal(t, expectedDuration, opts.MaxWaitForOrderEntry)

		// Assert that the proxies were parsed correctly by the custom hook
		require.Len(t, opts.Proxies, 2)
		assert.Equal(t, "http://proxy1.com", opts.Proxies[0].String())
		assert.Equal(t, "https://user:<EMAIL>", opts.Proxies[1].String())

		// Assert that the slice of strings was parsed correctly
		require.Len(t, opts.LoadMarket, 2)
		assert.Equal(t, "BTC/USD", opts.LoadMarket[0])
		assert.Equal(t, "ETH/USD", opts.LoadMarket[1])
	})
}

func TestFromGoStruct_ErrorCases(t *testing.T) {
	t.Run("Unmarshallable Type", func(t *testing.T) {
		_, err := FromGoStruct(Unmarshallable{Data: make(chan int)})
		require.Error(t, err, "FromGoStruct should have returned an error for unmarshallable type")
		assert.Contains(t, err.Error(), "failed to marshal struct to JSON")
	})

	t.Run("JSON Unmarshal to Map Fails", func(t *testing.T) {
		_, err := FromGoStruct("a plain string")
		require.Error(t, err, "FromGoStruct should have returned an error for non-object JSON")
		assert.Contains(t, err.Error(), "failed to unmarshal JSON to map")
	})
}

func TestToGoStruct_ErrorCases(t *testing.T) {
	t.Run("Mismatched Field Type", func(t *testing.T) {
		protoStruct, err := structpb.NewStruct(map[string]interface{}{
			"name": "Alice",
			"age":  "thirty",
		})
		require.NoError(t, err)

		// With WeaklyTypedInput, this might not fail anymore, but the custom hooks are more explicit.
		// Let's test a case that WeaklyTypedInput won't solve.
		_, err = ToGoStruct[Simple](protoStruct)
		require.Error(t, err, "Expected an error when decoding with a mismatched field type")
		assert.Contains(t, err.Error(), "cannot parse") // mapstructure's error for weak typing failure
	})

	t.Run("Nil Input gives Zero Value", func(t *testing.T) {
		result, err := ToGoStruct[Simple](nil)
		require.NoError(t, err)
		assert.Equal(t, Simple{}, result, "Expected a zero-value struct for nil input")
	})
}
