module.exports = {
  branches: ['master'],
  plugins: [
    // Your custom commit analyzer rules
    ['@semantic-release/commit-analyzer', {
      preset: 'conventionalcommits',
      releaseRules: [
        { type: 'feat', release: 'minor' },
        { type: 'fix', release: 'patch' },
        { type: 'refactor', release: 'patch' },
        { type: 'chore', release: 'patch' },
        { type: 'chore', scope: "deploy", release: false },
        { type: 'docs', release: false },
        { type: 'test', release: false },
        { type: 'ci', release: false },
        { scope: 'no-release', release: false },
      ],
      parserOpts: {
        noteKeywords: ['BREAKING CHANGE', 'BREAKING CHANGES'],
      },
    }],
    // Your custom release notes generator
    ['@semantic-release/release-notes-generator', {
      preset: 'conventionalcommits',
      presetConfig: {
        types: [
          { type: 'feat',            section: '✨ Features',         hidden: false },
          { type: 'fix',             section: '🐛 Bug Fixes',        hidden: false },
          { type: 'docs',            section: '📝 Docs',             hidden: false },
          { type: 'refactor',        section: '♻️ Refactor',         hidden: false },
          { type: 'revert',          section: '🕐 Reverts',          hidden: false },
          { type: 'ci',              section: '💫 CI/CD',            hidden: false },
          { type: 'test',            section: '✅ Tests',            hidden: false },
          { type: 'chore',           section: '📦 Chores',           hidden: false },
          { type: 'BREAKING CHANGE', section: '⚠️ Breaking Changes', hidden: false },
        ],
      },
    }],
    // Plugin to update CHANGELOG.md (from package.json)
    ['@semantic-release/changelog', {
      changelogFile: 'CHANGELOG.md',
    }],
    // Plugin to update version.go (from package.json)
    ['@semantic-release/exec', {
      "prepareCmd": "npm run update-versions -- ${nextRelease.version} && make build-docs-gen"
    }],
    // Plugin to update package.json (from package.json)
    ['@semantic-release/npm', {
      npmPublish: false,
    }],
    // Plugin to commit changed files (from package.json)
    ['@semantic-release/git', {
      assets: [
        'CHANGELOG.md',
        'package.json',
        'version.go',
        'internal/app/routes.go',
        'docs/docs.go',
        'docs/swagger.json'
      ],
      message: 'chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}',
    }],
    // Plugin to create the GitHub Release
    '@semantic-release/github',
  ],
};