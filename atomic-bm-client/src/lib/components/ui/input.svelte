<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { cn } from '$lib/utils';
	import { createEventDispatcher } from 'svelte';
	import Icon from './icon.svelte';
	import { getContentFromClipboard } from '$utils';

	const d = createEventDispatcher();
	export let value: any = '',
		label = '',
		labelClass = '',
		placeholder = '',
		description = '',
		wrapperClass = '',
		descriptionClass = '',
		type = 'text',
		id = '',
		pasteBtn = false,
		required = false,
		disabled = false,
		multiple = false,
		maxlength: number | undefined = undefined,
		showPassword = false;

	$: passwordIcon = showPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline';

	function togglePasswordView() {
		showPassword = !showPassword;
	}
	function typeAction(node: any) {
		node.type = type;
	}
</script>

<label for={id} class={cn('relative flex w-full flex-col items-start gap-0.5', wrapperClass)}>
	{#if label}
		<div class="ml-1 flex flex-col items-start">
			<span class={cn('text-xs', labelClass)}>
				{label}
				{#if required}
					<span class="text-red-500">*</span>
				{/if}
			</span>
			{#if description}
				<span class={cn('text-start text-[0.65rem] text-gray-500', descriptionClass)}>
					{@html description}
				</span>
			{/if}
		</div>
	{/if}
	{#if showPassword}
		<input
			{disabled}
			{required}
			{id}
			{placeholder}
			maxlength={maxlength || undefined}
			type="text"
			class={cn(
				'w-full rounded-md border border-gray-800 bg-inherit p-2.5 text-sm shadow-md outline-none transition-all placeholder:text-xs hover:border-gray-700 focus:border-gray-700',
				$$props.class
			)}
			bind:value
			on:input
			on:keydown
			on:blur
			on:keyup
			on:blur
			on:focus
		/>
	{:else if type === 'textarea'}
		<textarea
			{maxlength}
			{disabled}
			{required}
			{id}
			{placeholder}
			class={cn(
				'max-h-[120px] min-h-[70px] w-full rounded-md border border-gray-800 bg-inherit p-2.5 text-sm shadow-md outline-none transition-all placeholder:text-xs hover:border-gray-700 focus:border-gray-700',
				$$props.class
			)}
			bind:value
			on:input
			on:keydown
			on:blur
			on:keyup
			on:blur
			on:focus
		/>
	{:else}
		<input
			{maxlength}
			{disabled}
			{required}
			{id}
			{placeholder}
			multiple={type === 'file' && multiple}
			use:typeAction
			class={cn(
				'w-full rounded-md border border-gray-800 bg-inherit p-2.5 text-sm shadow-md outline-none transition-all placeholder:text-xs hover:border-gray-700 focus:border-gray-700',
				$$props.class
			)}
			bind:value
			on:input
			on:keydown
			on:blur
			on:keyup
			on:blur
			on:focus
		/>
	{/if}
	{#if type === 'password'}
		<Icon
			icon={passwordIcon}
			btn
			class="absolute bottom-0 right-2 z-20 mb-1.5 text-gray-500"
			size="large"
			on:click={togglePasswordView}
		/>
	{:else if pasteBtn}
		<Icon
			title="Colar conteúdo da área de transferência"
			icon="clarity:paste-line"
			btn
			class="absolute bottom-0 right-2 z-20 mb-2 text-white"
			size="large"
			on:click={async () => d('paste', await getContentFromClipboard())}
		/>
	{/if}
</label>

<style>
	/* Change the white to any color */
	input:-webkit-autofill,
	input:-webkit-autofill:hover,
	input:-webkit-autofill:focus,
	input:-webkit-autofill:active {
		-webkit-box-shadow: 0 0 0 30px #000000f5 inset !important;
	}

	/*Change text in autofill textbox*/
	input:-webkit-autofill {
		-webkit-text-fill-color: white !important;
	}
</style>
