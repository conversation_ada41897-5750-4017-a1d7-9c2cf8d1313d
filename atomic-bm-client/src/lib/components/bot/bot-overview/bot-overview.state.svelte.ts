import type { Bot } from '$client/api.schemas';

export interface BotStateData {
    activeOrders: number;
    stockBalance: number;
    moneyBalance: number;
    buyTopSpread: number;
    buyRefSpread: number;
    buyTopPrice: number;
    buyRefPrice: number;
    sellTopSpread: number;
    sellRefSpread: number;
    sellTopPrice: number;
    sellRefPrice: number;
    execStats: {
        buy: {
            avg_price: number;
            money_volume: number;
            volume: number;
        };
        sell: {
            avg_price: number;
            money_volume: number;
            volume: number;
        };
        time_frame: number;
    };
}

export interface BotWithState extends Bot {
    state?: BotStateData;
}

export interface BotFilters {
    exchanges: string[];
    assets: string[];
    accounts: string[];
    botIds: string[];
    status: string[];
}

export interface BotSummary {
    totalBots: number;
    activeBots: number;
    inactiveBots: number;
    errorBots: number;
    totalOrders: number;
    totalStock: number;
    totalMoney: number;
    tradingBots: number;
    successRate: number;
}

export class BotOverviewState {
    bots = $state<BotWithState[]>([]);
    isLoading = $state(false);
    error = $state<string | null>(null);
    filters = $state<BotFilters>({
        exchanges: [],
        assets: [],
        accounts: [],
        botIds: [],
        status: []
    });
    searchTerm = $state('');
    showDeleteDialog = $state(false);
    selectedBot = $state<BotWithState | null>(null);

    private createMockBotData(): BotWithState[] {
        const mockBots: Bot[] = [
            { id: 'bot-001', symbol: 'BTC-USDT', accountId: 'acc-001', tag: 'scalping', status: 'ACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'ACTIVE' },
            { id: 'bot-002', symbol: 'ETH-USDT', accountId: 'acc-002', tag: 'momentum', status: 'ACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'ACTIVE' },
            { id: 'bot-003', symbol: 'BNB-USDT', accountId: 'acc-003', tag: 'arbitrage', status: 'INACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'INACTIVE' },
            { id: 'bot-004', symbol: 'ADA-USDT', accountId: 'acc-004', tag: 'grid', status: 'ACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'ACTIVE' },
            { id: 'bot-005', symbol: 'SOL-USDT', accountId: 'acc-005', tag: 'dca', status: 'ERROR', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'ACTIVE' },
            { id: 'bot-006', symbol: 'DOT-USDT', accountId: 'acc-006', tag: 'swing', status: 'ACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'ACTIVE' },
            { id: 'bot-007', symbol: 'LINK-USDT', accountId: 'acc-007', tag: 'trend', status: 'INACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'INACTIVE' },
            { id: 'bot-008', symbol: 'AVAX-USDT', accountId: 'acc-008', tag: 'scalping', status: 'ACTIVE', createdAt: '2024-01-01', updatedAt: '2024-01-01', desiredStatus: 'ACTIVE' }
        ];

        return mockBots.map((bot, index) => {
            let seed = bot.id.charCodeAt(0) + bot.id.charCodeAt(bot.id.length - 1) + index;
            const random = (min: number, max: number) => {
                const x = Math.sin(seed++) * 10000;
                return min + (x - Math.floor(x)) * (max - min);
            };

            const mockState = {
                activeOrders: Math.floor(random(0, 15)),
                stockBalance: random(0.001, 2.0),
                moneyBalance: random(100, 5000),
                buyTopSpread: random(0.001, 0.005),
                buyRefSpread: random(0.0005, 0.003),
                buyTopPrice: random(40000, 50000),
                buyRefPrice: random(39900, 49900),
                sellTopSpread: random(0.001, 0.005),
                sellRefSpread: random(0.0005, 0.003),
                sellTopPrice: random(40100, 50100),
                sellRefPrice: random(40000, 50000),
                execStats: {
                    buy: {
                        avg_price: random(39950, 49950),
                        money_volume: random(500, 3000),
                        volume: random(0.01, 0.2)
                    },
                    sell: {
                        avg_price: random(40050, 50050),
                        money_volume: random(400, 2500),
                        volume: random(0.008, 0.15)
                    },
                    time_frame: 86400000000000 // 24h
                }
            };

            return {
                ...bot,
                state: mockState
            };
        });
    }

    async loadMockedBots(): Promise<BotWithState[]> {
        try {
            this.isLoading = true;
            this.error = null;

            await new Promise(resolve => setTimeout(resolve, 500));

            const botsWithState = this.createMockBotData();

            this.bots = botsWithState;
            return botsWithState;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to load mocked bots';
            this.error = errorMessage;
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async loadBots(): Promise<BotWithState[]> {
        try {
            this.isLoading = true;
            this.error = null;

            await new Promise(resolve => setTimeout(resolve, 500));

            const botsWithState = this.createMockBotData();

            this.bots = botsWithState;
            return botsWithState;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to load bots';
            this.error = errorMessage;
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    async refreshBots(): Promise<BotWithState[]> {
        return this.loadMockedBots();
    }

    getBotById(id: string): BotWithState | undefined {
        return this.bots.find(bot => bot.id === id);
    }

    updateBotState(botId: string, state: BotStateData): void {
        const bot = this.bots.find(b => b.id === botId);
        if (bot) {
            bot.state = state;
        }
    }

    clear(): void {
        this.bots = [];
        this.error = null;
        this.isLoading = false;
        this.filters = {
            exchanges: [],
            assets: [],
            accounts: [],
            botIds: [],
            status: []
        };
        this.searchTerm = '';
        this.selectedBot = null;
    }

    getFilteredBots(): BotWithState[] {
        let filtered = this.bots;

        if (this.filters.exchanges.length > 0) {
            filtered = filtered.filter(bot =>
                this.filters.exchanges.includes('Binance') // Mock exchange
            );
        }

        if (this.filters.accounts.length > 0) {
            filtered = filtered.filter(bot =>
                this.filters.accounts.includes(bot.accountId)
            );
        }

        if (this.filters.botIds.length > 0) {
            filtered = filtered.filter(bot =>
                this.filters.botIds.includes(bot.id)
            );
        }

        if (this.filters.status.length > 0) {
            filtered = filtered.filter(bot =>
                this.filters.status.includes(bot.status)
            );
        }

        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(bot =>
                bot.id.toLowerCase().includes(term) ||
                bot.symbol.toLowerCase().includes(term) ||
                bot.tag.toLowerCase().includes(term) ||
                bot.accountId.toLowerCase().includes(term)
            );
        }

        return filtered;
    }

    getBotsByStatus(status: string): BotWithState[] {
        return this.bots.filter(bot => bot.status === status);
    }

    getBotsByAccount(accountId: string): BotWithState[] {
        return this.bots.filter(bot => bot.accountId === accountId);
    }

    getBotsBySymbol(symbol: string): BotWithState[] {
        return this.bots.filter(bot => bot.symbol === symbol);
    }

    getActiveBots(): BotWithState[] {
        return this.bots.filter(bot => bot.status === 'ACTIVE');
    }

    getInactiveBots(): BotWithState[] {
        return this.bots.filter(bot => bot.status === 'INACTIVE');
    }

    getBotsWithErrors(): BotWithState[] {
        return this.bots.filter(bot => bot.status === 'ERROR');
    }

    getBotsWithActiveOrders(): BotWithState[] {
        return this.bots.filter(bot => bot.state?.activeOrders && bot.state.activeOrders > 0);
    }

    getTotalActiveOrders(): number {
        return this.bots.reduce((total, bot) =>
            total + (bot.state?.activeOrders || 0), 0
        );
    }

    getTotalStockBalance(): number {
        return this.bots.reduce((total, bot) =>
            total + (bot.state?.stockBalance || 0), 0
        );
    }

    getTotalMoneyBalance(): number {
        return this.bots.reduce((total, bot) =>
            total + (bot.state?.moneyBalance || 0), 0
        );
    }

    getBotsWithTradingActivity(): BotWithState[] {
        return this.bots.filter(bot => {
            const state = bot.state;
            if (!state) return false;

            return (state.execStats.buy.volume > 0 || state.execStats.sell.volume > 0);
        });
    }

    getBotsWithHighActivity(threshold: number = 0.1): BotWithState[] {
        return this.bots.filter(bot => {
            const state = bot.state;
            if (!state) return false;

            const totalVolume = state.execStats.buy.volume + state.execStats.sell.volume;
            return totalVolume > threshold;
        });
    }

    getBotsWithLowBalance(threshold: number = 0.01): BotWithState[] {
        return this.bots.filter(bot => {
            const state = bot.state;
            if (!state) return false;

            return state.stockBalance < threshold || state.moneyBalance < 100;
        });
    }

    getBotsWithHighSpread(threshold: number = 0.005): BotWithState[] {
        return this.bots.filter(bot => {
            const state = bot.state;
            if (!state) return false;

            return state.buyTopSpread > threshold || state.sellTopSpread > threshold;
        });
    }

    getBotsSummary(): BotSummary {
        const totalBots = this.bots.length;
        const activeBots = this.getActiveBots().length;
        const inactiveBots = this.getInactiveBots().length;
        const errorBots = this.getBotsWithErrors().length;
        const totalOrders = this.getTotalActiveOrders();
        const totalStock = this.getTotalStockBalance();
        const totalMoney = this.getTotalMoneyBalance();
        const tradingBots = this.getBotsWithTradingActivity().length;

        return {
            totalBots,
            activeBots,
            inactiveBots,
            errorBots,
            totalOrders,
            totalStock,
            totalMoney,
            tradingBots,
            successRate: totalBots > 0 ? (activeBots / totalBots) * 100 : 0
        };
    }

    updateFilters(newFilters: Partial<BotFilters>): void {
        this.filters = { ...this.filters, ...newFilters };
    }

    clearFilters(): void {
        this.filters = {
            exchanges: [],
            assets: [],
            accounts: [],
            botIds: [],
            status: []
        };
    }

    updateSearchTerm(term: string): void {
        this.searchTerm = term;
    }

    clearError(): void {
        this.error = null;
    }

    selectBot(bot: BotWithState | null): void {
        this.selectedBot = bot;
    }

    setShowDeleteDialog(show: boolean): void {
        this.showDeleteDialog = show;
    }

    getAvailableFilterOptions() {
        const exchanges = [...new Set(this.bots.map(() => 'Binance'))]; // Mock
        const accounts = [...new Set(this.bots.map(bot => bot.accountId))];
        const botIds = [...new Set(this.bots.map(bot => bot.id))];
        const statuses = [...new Set(this.bots.map(bot => bot.status))];
        const assets = [...new Set(this.bots.map(bot => bot.symbol.split('-')[0]))]; // Mock base assets

        return {
            exchanges,
            accounts,
            botIds,
            statuses,
            assets
        };
    }
}
