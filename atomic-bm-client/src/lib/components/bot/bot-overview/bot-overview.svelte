<script lang="ts">
	import BotCard from '../bot-card.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
	import { BotOverviewState } from './bot-overview.state.svelte';
	import { onMount } from 'svelte';

	const botOverviewState = new BotOverviewState();

	onMount(() => {
		botOverviewState.loadMockedBots();
	});
</script>

<div class="w-full">
	{#if botOverviewState.isLoading}
		<div class="flex items-center justify-center p-8">
			<div class="flex flex-col items-center gap-2">
				<div class="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
				<span class="text-sm text-gray-400">Loading bot states...</span>
			</div>
		</div>
	{:else if botOverviewState.error}
		<div class="flex items-center justify-center p-8">
			<div class="flex flex-col items-center gap-2 text-center">
				<Icon icon="ph:warning" class="text-2xl text-red-500" />
				<span class="text-sm text-red-400">Failed to load bot states</span>
				<span class="text-xs text-gray-500">{botOverviewState.error}</span>
				<button
					class="mt-2 rounded bg-blue-600 px-3 py-1 text-xs text-white transition-colors hover:bg-blue-700"
					onclick={() => botOverviewState.refreshBots()}
				>
					Retry
				</button>
			</div>
		</div>
	{:else if !botOverviewState.bots.length}
		<div class="flex items-center justify-center p-8">
			<div class="flex flex-col items-center gap-2 text-center">
				<Icon icon="ph:robot" class="text-2xl text-gray-500" />
				<span class="text-sm text-gray-400">No bots found</span>
				<span class="text-xs text-gray-500">Create your first bot to get started</span>
			</div>
		</div>
	{:else}
		<div class="grid grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
			{#each botOverviewState.getFilteredBots() as bot (bot.id)}
				<BotCard {bot} onClick={() => {}} />
			{/each}
		</div>
	{/if}
</div>
