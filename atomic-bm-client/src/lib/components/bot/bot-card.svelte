<script lang="ts">
	import type { Bot } from '$client/api.schemas';

	interface BotStateData {
		activeOrders: number;
		stockBalance: number;
		moneyBalance: number;
		buyTopSpread: number;
		buyRefSpread: number;
		buyTopPrice: number;
		buyRefPrice: number;
		sellTopSpread: number;
		sellRefSpread: number;
		sellTopPrice: number;
		sellRefPrice: number;
		execStats: {
			buy: {
				avg_price: number;
				money_volume: number;
				volume: number;
			};
			sell: {
				avg_price: number;
				money_volume: number;
				volume: number;
			};
			time_frame: number;
		};
	}

	interface BotWithState extends Bot {
		state?: BotStateData;
	}

	interface Props {
		bot: BotWithState;
		onClick?: () => void;
	}

	let { bot, onClick }: Props = $props();

	const mockState: BotStateData = {
		activeOrders: 5,
		stockBalance: 0.12345,
		moneyBalance: 1250.5,
		buyTopSpread: 0.0025,
		buyRefSpread: 0.0015,
		buyTopPrice: 45000.0,
		buyRefPrice: 44950.0,
		sellTopSpread: 0.002,
		sellRefSpread: 0.001,
		sellTopPrice: 45100.0,
		sellRefPrice: 45050.0,
		execStats: {
			buy: {
				avg_price: 44975.0,
				money_volume: 2250.0,
				volume: 0.05
			},
			sell: {
				avg_price: 45075.0,
				money_volume: 1800.0,
				volume: 0.04
			},
			time_frame: 86400000000000 // 24h
		}
	};

	const botState = $derived(bot.state || mockState);

	function formatAmount(amount: number, decimalPlaces: number = 2): string {
		if (!amount || isNaN(amount)) {
			return '0';
		}

		const absValue = Math.abs(amount);

		if (absValue >= 1000000000) {
			return `${(amount / 1000000000).toFixed(2).replace(/\.0+$/, '')}B`;
		} else if (absValue >= 1000000) {
			return `${(amount / 1000000).toFixed(2).replace(/\.0+$/, '')}M`;
		} else if (absValue >= 10) {
			return addThousandsSeparator(amount.toFixed(2).replace(/\.0+$/, ''));
		}

		if (absValue > 0 && absValue < 0.0001) {
			if (absValue < 0.00000001) {
				return amount.toExponential(2);
			}
			const str = absValue.toString();
			const match = str.match(/\.0*/);
			if (match) {
				const leadingZeros = match[0].length - 1;
				const sigDigits = leadingZeros + 2;
				return amount.toFixed(sigDigits);
			}
		}

		return amount.toFixed(decimalPlaces);
	}

	function addThousandsSeparator(numStr: string): string {
		return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
	}

	function formatSpread(spread?: number): string {
		if (!spread || isNaN(spread)) return '0%';
		return `${(spread * 100).toFixed(2)}%`;
	}

	function calculateInventoryValue(): number {
		return botState.stockBalance * botState.buyTopPrice;
	}

	function getOrdersBadgeClass(orderCount: number): string {
		if (orderCount === 0) {
			return 'bg-gray-600 text-gray-300';
		} else if (orderCount <= 5) {
			return 'bg-green-600 text-green-100';
		} else if (orderCount <= 10) {
			return 'bg-yellow-600 text-yellow-100';
		} else {
			return 'bg-red-600 text-red-100';
		}
	}

	function getTimeframeLabel(timeframe: number): string {
		switch (timeframe) {
			case 86400000000000:
				return '24H TRADING ACTIVITY';
			case 3600000000000:
				return '1H TRADING ACTIVITY';
			case 300000000000:
				return '5M TRADING ACTIVITY';
			case 604800000000000:
				return '7D TRADING ACTIVITY';
			case **************:
				return '12H TRADING ACTIVITY';
			default:
				return 'TRADING ACTIVITY';
		}
	}

	function handleCardClick() {
		if (onClick) {
			onClick();
		}
	}
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore event_directive_deprecated -->
<div
	class="flex h-[315px] cursor-pointer flex-col overflow-hidden rounded border border-gray-700/30 bg-dark-8 transition-all duration-200 hover:-translate-y-0.5 hover:border-gray-600/20 hover:shadow-lg"
	onclick={handleCardClick}
>
	<!-- Header section -->
	<div class="flex flex-col bg-dark-9 p-3">
		<div class="flex flex-col gap-1.5">
			<div class="flex flex-wrap items-center gap-2">
				<div class="text-base font-medium text-white">{bot.id}</div>
				<div class="text-sm text-gray-400">{bot.symbol}</div>
			</div>

			<div class="flex flex-wrap gap-1.5">
				<span class="rounded bg-gray-700 px-1.5 py-0.5 text-xs text-gray-300">Binance</span>
				<span class="rounded bg-blue-600/20 px-1.5 py-0.5 text-xs text-blue-400"
					>{bot.accountId}</span
				>
				<span class="rounded px-1.5 py-0.5 text-xs {getOrdersBadgeClass(botState.activeOrders)}">
					{botState.activeOrders} Orders
				</span>
			</div>
		</div>
	</div>

	<!-- Balance section -->
	<div class="grid grid-cols-3 gap-2 bg-black/15 p-3">
		<div class="flex min-w-0 flex-col">
			<div class="mb-0.5 text-xs text-gray-400">Stock</div>
			<div
				class="overflow-hidden text-ellipsis whitespace-nowrap text-xs font-medium"
				title="{formatAmount(botState.stockBalance, 5)} BTC"
			>
				{formatAmount(botState.stockBalance, 5)} <span class="ml-0.5 text-xs opacity-85">BTC</span>
			</div>
		</div>
		<div class="flex min-w-0 flex-col">
			<div class="mb-0.5 text-xs text-gray-400">Value</div>
			<div
				class="overflow-hidden text-ellipsis whitespace-nowrap text-xs font-medium"
				title="{formatAmount(calculateInventoryValue(), 2)} USDT"
			>
				{formatAmount(calculateInventoryValue(), 2)}
				<span class="ml-0.5 text-xs opacity-85">USDT</span>
			</div>
		</div>
		<div class="flex min-w-0 flex-col">
			<div class="mb-0.5 text-xs text-gray-400">Money</div>
			<div
				class="overflow-hidden text-ellipsis whitespace-nowrap text-xs font-medium"
				title="{formatAmount(botState.moneyBalance, 2)} USDT"
			>
				{formatAmount(botState.moneyBalance, 2)} <span class="ml-0.5 text-xs opacity-85">USDT</span>
			</div>
		</div>
	</div>

	<!-- Trading Activity section -->
	<div class="box-border h-24 border-t border-gray-700/30 bg-black/15 p-1.5 pb-2">
		<div class="mb-1.5 text-xs font-medium uppercase tracking-wider text-gray-400">
			{getTimeframeLabel(botState.execStats.time_frame)}
		</div>

		<div class="grid grid-cols-3 gap-2">
			<!-- Buy side stats -->
			{#if botState.execStats.buy.volume > 0}
				<div class="flex min-w-0 flex-col">
					<div class="mb-0.5">
						<span class="text-xs font-medium text-green-500">Buy</span>
					</div>
					<div
						class="overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium"
						title="Volume: {formatAmount(botState.execStats.buy.volume, 5)} BTC"
					>
						{formatAmount(botState.execStats.buy.volume, 5)}
						<span class="text-xs opacity-85">BTC</span>
					</div>
					<div
						class="overflow-hidden text-ellipsis whitespace-nowrap text-xs text-gray-400"
						title="Value: {formatAmount(botState.execStats.buy.money_volume, 2)} USDT"
					>
						{formatAmount(botState.execStats.buy.money_volume, 2)}
						<span class="text-xs opacity-85">USDT</span>
					</div>
				</div>
			{/if}

			<!-- Sell side stats -->
			{#if botState.execStats.sell.volume > 0}
				<div class="flex min-w-0 flex-col">
					<div class="mb-0.5">
						<span class="text-xs font-medium text-red-500">Sell</span>
					</div>
					<div
						class="overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium"
						title="Volume: {formatAmount(botState.execStats.sell.volume, 5)} BTC"
					>
						{formatAmount(botState.execStats.sell.volume, 5)}
						<span class="text-xs opacity-85">BTC</span>
					</div>
					<div
						class="overflow-hidden text-ellipsis whitespace-nowrap text-xs text-gray-400"
						title="Value: {formatAmount(botState.execStats.sell.money_volume, 2)} USDT"
					>
						{formatAmount(botState.execStats.sell.money_volume, 2)}
						<span class="text-xs opacity-85">USDT</span>
					</div>
				</div>
			{/if}

			<!-- Total volume -->
			{#if botState.execStats.buy.volume > 0 || botState.execStats.sell.volume > 0}
				<div class="flex min-w-0 flex-col">
					<div class="mb-0.5">
						<span class="text-xs font-medium text-blue-500">Total</span>
					</div>
					<div
						class="overflow-hidden text-ellipsis whitespace-nowrap text-sm font-medium"
						title="Total Volume: {formatAmount(
							botState.execStats.buy.volume + botState.execStats.sell.volume,
							5
						)} BTC"
					>
						{formatAmount(botState.execStats.buy.volume + botState.execStats.sell.volume, 5)}
						<span class="text-xs opacity-85">BTC</span>
					</div>
					<div
						class="overflow-hidden text-ellipsis whitespace-nowrap text-xs text-gray-400"
						title="Total Value: {formatAmount(
							botState.execStats.buy.money_volume + botState.execStats.sell.money_volume,
							2
						)} USDT"
					>
						{formatAmount(
							botState.execStats.buy.money_volume + botState.execStats.sell.money_volume,
							2
						)} <span class="text-xs opacity-85">USDT</span>
					</div>
				</div>
			{/if}
		</div>
	</div>

	<!-- Trading metrics -->
	<div class="flex min-h-[120px] flex-1 flex-col p-2">
		<div class="mb-1.5 grid grid-cols-5 gap-1 text-xs text-gray-400">
			<div class="flex items-center"></div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					Top Spread
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					Ref Spread
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					Top Price
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					Ref Price
				</div>
			</div>
		</div>

		<div class="grid grid-cols-5 gap-1 border-t border-gray-700/30 py-1">
			<div class="flex items-center">
				<span class="text-xs text-green-500">Buy</span>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					{formatSpread(botState.buyTopSpread)}
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					{formatSpread(botState.buyRefSpread)}
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs font-medium">
					{formatAmount(botState.buyTopPrice, 2)}
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs font-medium">
					{formatAmount(botState.buyRefPrice, 2)}
				</div>
			</div>
		</div>

		<!-- Sell Side -->
		<div class="grid grid-cols-5 gap-1 border-t border-gray-700/30 py-1">
			<div class="flex items-center">
				<span class="text-xs text-red-500">Sell</span>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					{formatSpread(botState.sellTopSpread)}
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs">
					{formatSpread(botState.sellRefSpread)}
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs font-medium">
					{formatAmount(botState.sellTopPrice, 2)}
				</div>
			</div>
			<div class="flex justify-end">
				<div class="overflow-hidden text-ellipsis whitespace-nowrap text-right text-xs font-medium">
					{formatAmount(botState.sellRefPrice, 2)}
				</div>
			</div>
		</div>
	</div>
</div>
