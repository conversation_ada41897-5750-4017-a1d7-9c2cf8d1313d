/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	Error,
	GetExchangesParams,
	ResponseArrayExchange,
	ResponseExchange
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType } from '../utils/api-mutator';

/**
 * List exchanges with optional filters and pagination
 * @summary List exchanges.
 */
export const getExchanges = (params?: GetExchangesParams) => {
	return customInstance<ResponseArrayExchange>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges`,
		method: 'GET',
		params
	});
};

export const getGetExchangesQueryKey = (params?: GetExchangesParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges`, ...(params ? [params] : [])] as const;
};

export const getGetExchangesQueryOptions = <
	TData = Awaited<ReturnType<typeof getExchanges>>,
	TError = ErrorType<Error>
>(
	params?: GetExchangesParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getExchanges>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetExchangesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getExchanges>>> = () =>
		getExchanges(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getExchanges>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetExchangesQueryResult = NonNullable<Awaited<ReturnType<typeof getExchanges>>>;
export type GetExchangesQueryError = ErrorType<Error>;

/**
 * @summary List exchanges.
 */
export const createGetExchanges = <
	TData = Awaited<ReturnType<typeof getExchanges>>,
	TError = ErrorType<Error>
>(
	params?: GetExchangesParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getExchanges>>, TError, TData>>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetExchangesQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Fetch an exchange by providing its exchangeID
 * @summary Fetch an exchange
 */
export const getExchangesExchangeId = (exchangeId: string) => {
	return customInstance<ResponseExchange>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges/${exchangeId}`,
		method: 'GET'
	});
};

export const getGetExchangesExchangeIdQueryKey = (exchangeId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges/${exchangeId}`] as const;
};

export const getGetExchangesExchangeIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getExchangesExchangeId>>,
	TError = ErrorType<Error>
>(
	exchangeId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getExchangesExchangeId>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetExchangesExchangeIdQueryKey(exchangeId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getExchangesExchangeId>>> = () =>
		getExchangesExchangeId(exchangeId);

	return { queryKey, queryFn, enabled: !!exchangeId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getExchangesExchangeId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetExchangesExchangeIdQueryResult = NonNullable<
	Awaited<ReturnType<typeof getExchangesExchangeId>>
>;
export type GetExchangesExchangeIdQueryError = ErrorType<Error>;

/**
 * @summary Fetch an exchange
 */
export const createGetExchangesExchangeId = <
	TData = Awaited<ReturnType<typeof getExchangesExchangeId>>,
	TError = ErrorType<Error>
>(
	exchangeId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getExchangesExchangeId>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetExchangesExchangeIdQueryOptions(exchangeId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};
