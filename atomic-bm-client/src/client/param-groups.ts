/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	CreateParamGroupRequest,
	Error,
	GetGroupsParams,
	GetParamsGroupsBotsParams,
	ResponseArrayBotParamGroup,
	ResponseArrayParamGroup,
	ResponseParamGroup
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * List param groups with optional filters and pagination
 * @summary List param groups
 */
export const getGroups = (params?: GetGroupsParams) => {
	return customInstance<ResponseArrayParamGroup>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/groups`,
		method: 'GET',
		params
	});
};

export const getGetGroupsQueryKey = (params?: GetGroupsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/groups`, ...(params ? [params] : [])] as const;
};

export const getGetGroupsQueryOptions = <
	TData = Awaited<ReturnType<typeof getGroups>>,
	TError = ErrorType<Error>
>(
	params?: GetGroupsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getGroups>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetGroupsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getGroups>>> = () => getGroups(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getGroups>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetGroupsQueryResult = NonNullable<Awaited<ReturnType<typeof getGroups>>>;
export type GetGroupsQueryError = ErrorType<Error>;

/**
 * @summary List param groups
 */
export const createGetGroups = <
	TData = Awaited<ReturnType<typeof getGroups>>,
	TError = ErrorType<Error>
>(
	params?: GetGroupsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getGroups>>, TError, TData>>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetGroupsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates a param group by providing a name
 * @summary Create a param group
 */
export const postGroups = (createParamGroupRequest: BodyType<CreateParamGroupRequest>) => {
	return customInstance<ResponseParamGroup>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/groups`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: createParamGroupRequest
	});
};

export const getPostGroupsMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postGroups>>,
		TError,
		{ data: BodyType<CreateParamGroupRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postGroups>>,
	TError,
	{ data: BodyType<CreateParamGroupRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postGroups>>,
		{ data: BodyType<CreateParamGroupRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postGroups(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostGroupsMutationResult = NonNullable<Awaited<ReturnType<typeof postGroups>>>;
export type PostGroupsMutationBody = BodyType<CreateParamGroupRequest>;
export type PostGroupsMutationError = ErrorType<Error>;

/**
 * @summary Create a param group
 */
export const createPostGroups = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postGroups>>,
		TError,
		{ data: BodyType<CreateParamGroupRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postGroups>>,
	TError,
	{ data: BodyType<CreateParamGroupRequest> },
	TContext
> => {
	const mutationOptions = getPostGroupsMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Delete a param group by providing its ID
 * @summary Delete a param group
 */
export const deleteGroupsGroupId = (groupId: string) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/groups/${groupId}`,
		method: 'DELETE'
	});
};

export const getDeleteGroupsGroupIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteGroupsGroupId>>,
		TError,
		{ groupId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof deleteGroupsGroupId>>,
	TError,
	{ groupId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteGroupsGroupId>>,
		{ groupId: string }
	> = (props) => {
		const { groupId } = props ?? {};

		return deleteGroupsGroupId(groupId);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteGroupsGroupIdMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteGroupsGroupId>>
>;

export type DeleteGroupsGroupIdMutationError = ErrorType<Error>;

/**
 * @summary Delete a param group
 */
export const createDeleteGroupsGroupId = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteGroupsGroupId>>,
		TError,
		{ groupId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof deleteGroupsGroupId>>,
	TError,
	{ groupId: string },
	TContext
> => {
	const mutationOptions = getDeleteGroupsGroupIdMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * List bot param group relations with optional filters and pagination
 * @summary List bot param group relations
 */
export const getParamsGroupsBots = (params?: GetParamsGroupsBotsParams) => {
	return customInstance<ResponseArrayBotParamGroup>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/bots`,
		method: 'GET',
		params
	});
};

export const getGetParamsGroupsBotsQueryKey = (params?: GetParamsGroupsBotsParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/bots`,
		...(params ? [params] : [])
	] as const;
};

export const getGetParamsGroupsBotsQueryOptions = <
	TData = Awaited<ReturnType<typeof getParamsGroupsBots>>,
	TError = ErrorType<Error>
>(
	params?: GetParamsGroupsBotsParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsGroupsBots>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetParamsGroupsBotsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getParamsGroupsBots>>> = () =>
		getParamsGroupsBots(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getParamsGroupsBots>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetParamsGroupsBotsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getParamsGroupsBots>>
>;
export type GetParamsGroupsBotsQueryError = ErrorType<Error>;

/**
 * @summary List bot param group relations
 */
export const createGetParamsGroupsBots = <
	TData = Awaited<ReturnType<typeof getParamsGroupsBots>>,
	TError = ErrorType<Error>
>(
	params?: GetParamsGroupsBotsParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsGroupsBots>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetParamsGroupsBotsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates relation between bot and param groups by providing bot and param group IDs
 * @summary Create bot param group relation
 */
export const postParamsGroupsGroupIdBotsBotId = (groupId: string, botId: string) => {
	return customInstance<ResponseArrayBotParamGroup>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/${groupId}/bots/${botId}`,
		method: 'POST'
	});
};

export const getPostParamsGroupsGroupIdBotsBotIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postParamsGroupsGroupIdBotsBotId>>,
		TError,
		{ groupId: string; botId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postParamsGroupsGroupIdBotsBotId>>,
	TError,
	{ groupId: string; botId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postParamsGroupsGroupIdBotsBotId>>,
		{ groupId: string; botId: string }
	> = (props) => {
		const { groupId, botId } = props ?? {};

		return postParamsGroupsGroupIdBotsBotId(groupId, botId);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostParamsGroupsGroupIdBotsBotIdMutationResult = NonNullable<
	Awaited<ReturnType<typeof postParamsGroupsGroupIdBotsBotId>>
>;

export type PostParamsGroupsGroupIdBotsBotIdMutationError = ErrorType<Error>;

/**
 * @summary Create bot param group relation
 */
export const createPostParamsGroupsGroupIdBotsBotId = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postParamsGroupsGroupIdBotsBotId>>,
		TError,
		{ groupId: string; botId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postParamsGroupsGroupIdBotsBotId>>,
	TError,
	{ groupId: string; botId: string },
	TContext
> => {
	const mutationOptions = getPostParamsGroupsGroupIdBotsBotIdMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Delete bot param group relations specified in the request payload
 * @summary Delete bot param group relations
 */
export const deleteParamsGroupsGroupIdBotsBotId = (groupId: string, botId: string) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/${groupId}/bots/${botId}`,
		method: 'DELETE'
	});
};

export const getDeleteParamsGroupsGroupIdBotsBotIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteParamsGroupsGroupIdBotsBotId>>,
		TError,
		{ groupId: string; botId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof deleteParamsGroupsGroupIdBotsBotId>>,
	TError,
	{ groupId: string; botId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteParamsGroupsGroupIdBotsBotId>>,
		{ groupId: string; botId: string }
	> = (props) => {
		const { groupId, botId } = props ?? {};

		return deleteParamsGroupsGroupIdBotsBotId(groupId, botId);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteParamsGroupsGroupIdBotsBotIdMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteParamsGroupsGroupIdBotsBotId>>
>;

export type DeleteParamsGroupsGroupIdBotsBotIdMutationError = ErrorType<Error>;

/**
 * @summary Delete bot param group relations
 */
export const createDeleteParamsGroupsGroupIdBotsBotId = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteParamsGroupsGroupIdBotsBotId>>,
		TError,
		{ groupId: string; botId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof deleteParamsGroupsGroupIdBotsBotId>>,
	TError,
	{ groupId: string; botId: string },
	TContext
> => {
	const mutationOptions = getDeleteParamsGroupsGroupIdBotsBotIdMutationOptions(options);

	return createMutation(mutationOptions);
};
