/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	CreateBotRequest,
	Error,
	GetBotsParams,
	PageResponseArrayBot,
	ResponseArrayListBotsStateResponse,
	ResponseBot,
	ResponseGetBotStateResponse,
	UpdateBotRequest
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * Retrieves a list of bots with optional filtering and pagination parameters.
 * @summary Lists bots with custom filters.
 */
export const getBots = (params?: GetBotsParams) => {
	return customInstance<PageResponseArrayBot>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots`,
		method: 'GET',
		params
	});
};

export const getGetBotsQueryKey = (params?: GetBotsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots`, ...(params ? [params] : [])] as const;
};

export const getGetBotsQueryOptions = <
	TData = Awaited<ReturnType<typeof getBots>>,
	TError = ErrorType<Error>
>(
	params?: GetBotsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBots>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBots>>> = () => getBots(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBots>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsQueryResult = NonNullable<Awaited<ReturnType<typeof getBots>>>;
export type GetBotsQueryError = ErrorType<Error>;

/**
 * @summary Lists bots with custom filters.
 */
export const createGetBots = <
	TData = Awaited<ReturnType<typeof getBots>>,
	TError = ErrorType<Error>
>(
	params?: GetBotsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBots>>, TError, TData>>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates a new bot with the specified parameters.
 * @summary Create a new bot.
 */
export const postBots = (createBotRequest: BodyType<CreateBotRequest>) => {
	return customInstance<ResponseBot>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: createBotRequest
	});
};

export const getPostBotsMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postBots>>,
		TError,
		{ data: BodyType<CreateBotRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postBots>>,
	TError,
	{ data: BodyType<CreateBotRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postBots>>,
		{ data: BodyType<CreateBotRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postBots(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostBotsMutationResult = NonNullable<Awaited<ReturnType<typeof postBots>>>;
export type PostBotsMutationBody = BodyType<CreateBotRequest>;
export type PostBotsMutationError = ErrorType<Error>;

/**
 * @summary Create a new bot.
 */
export const createPostBots = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postBots>>,
		TError,
		{ data: BodyType<CreateBotRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postBots>>,
	TError,
	{ data: BodyType<CreateBotRequest> },
	TContext
> => {
	const mutationOptions = getPostBotsMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Retrieves a cached state of all bots. The data is populated by a background worker.
 * @summary Get bots state.
 */
export const getBotsState = () => {
	return customInstance<ResponseArrayListBotsStateResponse>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/state`,
		method: 'GET'
	});
};

export const getGetBotsStateQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/state`] as const;
};

export const getGetBotsStateQueryOptions = <
	TData = Awaited<ReturnType<typeof getBotsState>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsState>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsStateQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotsState>>> = () => getBotsState();

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBotsState>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsStateQueryResult = NonNullable<Awaited<ReturnType<typeof getBotsState>>>;
export type GetBotsStateQueryError = ErrorType<Error>;

/**
 * @summary Get bots state.
 */
export const createGetBotsState = <
	TData = Awaited<ReturnType<typeof getBotsState>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsState>>, TError, TData>>;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsStateQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Retrieve a bot with the specified ID.
 * @summary Retrieve a bot by ID.
 */
export const getBotsBotId = (botId: string) => {
	return customInstance<ResponseBot>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`,
		method: 'GET'
	});
};

export const getGetBotsBotIdQueryKey = (botId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`] as const;
};

export const getGetBotsBotIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getBotsBotId>>,
	TError = ErrorType<Error>
>(
	botId: string,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotId>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsBotIdQueryKey(botId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotsBotId>>> = () =>
		getBotsBotId(botId);

	return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBotsBotId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsBotIdQueryResult = NonNullable<Awaited<ReturnType<typeof getBotsBotId>>>;
export type GetBotsBotIdQueryError = ErrorType<Error>;

/**
 * @summary Retrieve a bot by ID.
 */
export const createGetBotsBotId = <
	TData = Awaited<ReturnType<typeof getBotsBotId>>,
	TError = ErrorType<Error>
>(
	botId: string,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotId>>, TError, TData>>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsBotIdQueryOptions(botId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Delete a bot with the specified ID.
 * @summary Delete a bot by ID.
 */
export const deleteBotsBotId = (botId: string) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`,
		method: 'DELETE'
	});
};

export const getDeleteBotsBotIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteBotsBotId>>,
		TError,
		{ botId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof deleteBotsBotId>>,
	TError,
	{ botId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteBotsBotId>>,
		{ botId: string }
	> = (props) => {
		const { botId } = props ?? {};

		return deleteBotsBotId(botId);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteBotsBotIdMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteBotsBotId>>
>;

export type DeleteBotsBotIdMutationError = ErrorType<Error>;

/**
 * @summary Delete a bot by ID.
 */
export const createDeleteBotsBotId = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteBotsBotId>>,
		TError,
		{ botId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof deleteBotsBotId>>,
	TError,
	{ botId: string },
	TContext
> => {
	const mutationOptions = getDeleteBotsBotIdMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Update an existing bot with the specified ID by providing account ID, symbol, region, or status.
You need to ensure that at least one field is provided in the update request.
 * @summary Update an existing bot by ID.
 */
export const patchBotsBotId = (botId: string, updateBotRequest: BodyType<UpdateBotRequest>) => {
	return customInstance<ResponseBot>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`,
		method: 'PATCH',
		headers: { 'Content-Type': 'application/json' },
		data: updateBotRequest
	});
};

export const getPatchBotsBotIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof patchBotsBotId>>,
		TError,
		{ botId: string; data: BodyType<UpdateBotRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof patchBotsBotId>>,
	TError,
	{ botId: string; data: BodyType<UpdateBotRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof patchBotsBotId>>,
		{ botId: string; data: BodyType<UpdateBotRequest> }
	> = (props) => {
		const { botId, data } = props ?? {};

		return patchBotsBotId(botId, data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PatchBotsBotIdMutationResult = NonNullable<Awaited<ReturnType<typeof patchBotsBotId>>>;
export type PatchBotsBotIdMutationBody = BodyType<UpdateBotRequest>;
export type PatchBotsBotIdMutationError = ErrorType<Error>;

/**
 * @summary Update an existing bot by ID.
 */
export const createPatchBotsBotId = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof patchBotsBotId>>,
		TError,
		{ botId: string; data: BodyType<UpdateBotRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof patchBotsBotId>>,
	TError,
	{ botId: string; data: BodyType<UpdateBotRequest> },
	TContext
> => {
	const mutationOptions = getPatchBotsBotIdMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * It creates a bot based on the botId.
 * @summary StartBot create a new bot if not exists.
 */
export const postBotsBotIdStart = (botId: string) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/start`,
		method: 'POST'
	});
};

export const getPostBotsBotIdStartMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postBotsBotIdStart>>,
		TError,
		{ botId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postBotsBotIdStart>>,
	TError,
	{ botId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postBotsBotIdStart>>,
		{ botId: string }
	> = (props) => {
		const { botId } = props ?? {};

		return postBotsBotIdStart(botId);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostBotsBotIdStartMutationResult = NonNullable<
	Awaited<ReturnType<typeof postBotsBotIdStart>>
>;

export type PostBotsBotIdStartMutationError = ErrorType<Error>;

/**
 * @summary StartBot create a new bot if not exists.
 */
export const createPostBotsBotIdStart = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postBotsBotIdStart>>,
		TError,
		{ botId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postBotsBotIdStart>>,
	TError,
	{ botId: string },
	TContext
> => {
	const mutationOptions = getPostBotsBotIdStartMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Retrieves the current state of a specific bot from the botregional service. It uses a short-lived cache to reduce load.
 * @summary Get bot state.
 */
export const getBotsBotIdState = (botId: string) => {
	return customInstance<ResponseGetBotStateResponse>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/state`,
		method: 'GET'
	});
};

export const getGetBotsBotIdStateQueryKey = (botId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/state`] as const;
};

export const getGetBotsBotIdStateQueryOptions = <
	TData = Awaited<ReturnType<typeof getBotsBotIdState>>,
	TError = ErrorType<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotIdState>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsBotIdStateQueryKey(botId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotsBotIdState>>> = () =>
		getBotsBotIdState(botId);

	return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBotsBotIdState>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsBotIdStateQueryResult = NonNullable<
	Awaited<ReturnType<typeof getBotsBotIdState>>
>;
export type GetBotsBotIdStateQueryError = ErrorType<Error>;

/**
 * @summary Get bot state.
 */
export const createGetBotsBotIdState = <
	TData = Awaited<ReturnType<typeof getBotsBotIdState>>,
	TError = ErrorType<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotIdState>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsBotIdStateQueryOptions(botId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * It stops a bot based on the botId.
 * @summary StopBot stop a bot if exists.
 */
export const postBotsBotIdStop = (botId: string) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/stop`,
		method: 'POST'
	});
};

export const getPostBotsBotIdStopMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postBotsBotIdStop>>,
		TError,
		{ botId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postBotsBotIdStop>>,
	TError,
	{ botId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postBotsBotIdStop>>,
		{ botId: string }
	> = (props) => {
		const { botId } = props ?? {};

		return postBotsBotIdStop(botId);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostBotsBotIdStopMutationResult = NonNullable<
	Awaited<ReturnType<typeof postBotsBotIdStop>>
>;

export type PostBotsBotIdStopMutationError = ErrorType<Error>;

/**
 * @summary StopBot stop a bot if exists.
 */
export const createPostBotsBotIdStop = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postBotsBotIdStop>>,
		TError,
		{ botId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postBotsBotIdStop>>,
	TError,
	{ botId: string },
	TContext
> => {
	const mutationOptions = getPostBotsBotIdStopMutationOptions(options);

	return createMutation(mutationOptions);
};
