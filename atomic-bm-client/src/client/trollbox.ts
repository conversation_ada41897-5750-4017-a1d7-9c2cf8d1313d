/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	Error,
	GetTrollboxHistoryParams,
	ResponseArrayMessage,
	ResponseMessageHistoryResponse,
	SearchMessagesByContentRequest,
	SearchMessagesByTimeRangeRequest
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * Retrieve messages that fall within the specified time range.
 * @summary Search messages by time range.
 */
export const postTrollboxFilter = (
	searchMessagesByTimeRangeRequest: BodyType<SearchMessagesByTimeRangeRequest>
) => {
	return customInstance<ResponseArrayMessage>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/filter`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: searchMessagesByTimeRangeRequest
	});
};

export const getPostTrollboxFilterMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postTrollboxFilter>>,
		TError,
		{ data: BodyType<SearchMessagesByTimeRangeRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postTrollboxFilter>>,
	TError,
	{ data: BodyType<SearchMessagesByTimeRangeRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postTrollboxFilter>>,
		{ data: BodyType<SearchMessagesByTimeRangeRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postTrollboxFilter(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostTrollboxFilterMutationResult = NonNullable<
	Awaited<ReturnType<typeof postTrollboxFilter>>
>;
export type PostTrollboxFilterMutationBody = BodyType<SearchMessagesByTimeRangeRequest>;
export type PostTrollboxFilterMutationError = ErrorType<Error>;

/**
 * @summary Search messages by time range.
 */
export const createPostTrollboxFilter = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postTrollboxFilter>>,
		TError,
		{ data: BodyType<SearchMessagesByTimeRangeRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postTrollboxFilter>>,
	TError,
	{ data: BodyType<SearchMessagesByTimeRangeRequest> },
	TContext
> => {
	const mutationOptions = getPostTrollboxFilterMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Retrieve chat message history with optional pagination parameters.
 * @summary Get chat message history.
 */
export const getTrollboxHistory = (params?: GetTrollboxHistoryParams) => {
	return customInstance<ResponseMessageHistoryResponse>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/history`,
		method: 'GET',
		params
	});
};

export const getGetTrollboxHistoryQueryKey = (params?: GetTrollboxHistoryParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/history`,
		...(params ? [params] : [])
	] as const;
};

export const getGetTrollboxHistoryQueryOptions = <
	TData = Awaited<ReturnType<typeof getTrollboxHistory>>,
	TError = ErrorType<Error>
>(
	params?: GetTrollboxHistoryParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getTrollboxHistory>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetTrollboxHistoryQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getTrollboxHistory>>> = () =>
		getTrollboxHistory(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getTrollboxHistory>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetTrollboxHistoryQueryResult = NonNullable<
	Awaited<ReturnType<typeof getTrollboxHistory>>
>;
export type GetTrollboxHistoryQueryError = ErrorType<Error>;

/**
 * @summary Get chat message history.
 */
export const createGetTrollboxHistory = <
	TData = Awaited<ReturnType<typeof getTrollboxHistory>>,
	TError = ErrorType<Error>
>(
	params?: GetTrollboxHistoryParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getTrollboxHistory>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetTrollboxHistoryQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Retrieve messages that match the specified content.
 * @summary Search messages by content.
 */
export const postTrollboxSearch = (
	searchMessagesByContentRequest: BodyType<SearchMessagesByContentRequest>
) => {
	return customInstance<ResponseArrayMessage>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/search`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: searchMessagesByContentRequest
	});
};

export const getPostTrollboxSearchMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postTrollboxSearch>>,
		TError,
		{ data: BodyType<SearchMessagesByContentRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postTrollboxSearch>>,
	TError,
	{ data: BodyType<SearchMessagesByContentRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postTrollboxSearch>>,
		{ data: BodyType<SearchMessagesByContentRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postTrollboxSearch(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostTrollboxSearchMutationResult = NonNullable<
	Awaited<ReturnType<typeof postTrollboxSearch>>
>;
export type PostTrollboxSearchMutationBody = BodyType<SearchMessagesByContentRequest>;
export type PostTrollboxSearchMutationError = ErrorType<Error>;

/**
 * @summary Search messages by content.
 */
export const createPostTrollboxSearch = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postTrollboxSearch>>,
		TError,
		{ data: BodyType<SearchMessagesByContentRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postTrollboxSearch>>,
	TError,
	{ data: BodyType<SearchMessagesByContentRequest> },
	TContext
> => {
	const mutationOptions = getPostTrollboxSearchMutationOptions(options);

	return createMutation(mutationOptions);
};
