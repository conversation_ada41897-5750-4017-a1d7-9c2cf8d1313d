/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	CreateParamsRequest,
	DeleteParamsRequest,
	Error,
	GetParamsParams,
	PageResponseArrayParam,
	ResponseArrayParam
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * List parameters with optional filtering and ordering based on scope or scopeId.
 * @summary List parameters.
 */
export const getParams = (params?: GetParamsParams) => {
	return customInstance<PageResponseArrayParam>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params`,
		method: 'GET',
		params
	});
};

export const getGetParamsQueryKey = (params?: GetParamsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/params`, ...(params ? [params] : [])] as const;
};

export const getGetParamsQueryOptions = <
	TData = Awaited<ReturnType<typeof getParams>>,
	TError = ErrorType<Error>
>(
	params?: GetParamsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getParams>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetParamsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getParams>>> = () => getParams(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getParams>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetParamsQueryResult = NonNullable<Awaited<ReturnType<typeof getParams>>>;
export type GetParamsQueryError = ErrorType<Error>;

/**
 * @summary List parameters.
 */
export const createGetParams = <
	TData = Awaited<ReturnType<typeof getParams>>,
	TError = ErrorType<Error>
>(
	params?: GetParamsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getParams>>, TError, TData>>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetParamsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates parameters for one or more scopes (gateway, account, bot, etc.) in a single request.
When the Scope is 'gateway' or 'account', the scopeId must be a valid accountId.
When the Scope is 'bot', the scopeId must be a valid botId.
When the Scope is 'param_group', the scopeId must be a valid paramGroupId.
 * @summary Create parameters.
 */
export const postParams = (createParamsRequest: BodyType<CreateParamsRequest>) => {
	return customInstance<ResponseArrayParam>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: createParamsRequest
	});
};

export const getPostParamsMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postParams>>,
		TError,
		{ data: BodyType<CreateParamsRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postParams>>,
	TError,
	{ data: BodyType<CreateParamsRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postParams>>,
		{ data: BodyType<CreateParamsRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postParams(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostParamsMutationResult = NonNullable<Awaited<ReturnType<typeof postParams>>>;
export type PostParamsMutationBody = BodyType<CreateParamsRequest>;
export type PostParamsMutationError = ErrorType<Error>;

/**
 * @summary Create parameters.
 */
export const createPostParams = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postParams>>,
		TError,
		{ data: BodyType<CreateParamsRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postParams>>,
	TError,
	{ data: BodyType<CreateParamsRequest> },
	TContext
> => {
	const mutationOptions = getPostParamsMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Delete parameters by their keys and scope with the specified scopeID.
 * @summary Delete parameters.
 */
export const postParamsBulkDelete = (deleteParamsRequest: BodyType<DeleteParamsRequest>) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params/bulkDelete`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: deleteParamsRequest
	});
};

export const getPostParamsBulkDeleteMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postParamsBulkDelete>>,
		TError,
		{ data: BodyType<DeleteParamsRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postParamsBulkDelete>>,
	TError,
	{ data: BodyType<DeleteParamsRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postParamsBulkDelete>>,
		{ data: BodyType<DeleteParamsRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postParamsBulkDelete(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostParamsBulkDeleteMutationResult = NonNullable<
	Awaited<ReturnType<typeof postParamsBulkDelete>>
>;
export type PostParamsBulkDeleteMutationBody = BodyType<DeleteParamsRequest>;
export type PostParamsBulkDeleteMutationError = ErrorType<Error>;

/**
 * @summary Delete parameters.
 */
export const createPostParamsBulkDelete = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postParamsBulkDelete>>,
		TError,
		{ data: BodyType<DeleteParamsRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postParamsBulkDelete>>,
	TError,
	{ data: BodyType<DeleteParamsRequest> },
	TContext
> => {
	const mutationOptions = getPostParamsBulkDeleteMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Gets the active parameters based on botId.
 * @summary Get active parameters.
 */
export const getParamsBotIdActiveParams = (botId: string) => {
	return customInstance<PageResponseArrayParam>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/params/${botId}/activeParams`,
		method: 'GET'
	});
};

export const getGetParamsBotIdActiveParamsQueryKey = (botId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/params/${botId}/activeParams`] as const;
};

export const getGetParamsBotIdActiveParamsQueryOptions = <
	TData = Awaited<ReturnType<typeof getParamsBotIdActiveParams>>,
	TError = ErrorType<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsBotIdActiveParams>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetParamsBotIdActiveParamsQueryKey(botId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getParamsBotIdActiveParams>>> = () =>
		getParamsBotIdActiveParams(botId);

	return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getParamsBotIdActiveParams>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetParamsBotIdActiveParamsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getParamsBotIdActiveParams>>
>;
export type GetParamsBotIdActiveParamsQueryError = ErrorType<Error>;

/**
 * @summary Get active parameters.
 */
export const createGetParamsBotIdActiveParams = <
	TData = Awaited<ReturnType<typeof getParamsBotIdActiveParams>>,
	TError = ErrorType<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsBotIdActiveParams>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetParamsBotIdActiveParamsQueryOptions(botId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};
