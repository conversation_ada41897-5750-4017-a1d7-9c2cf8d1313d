/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	CreateAccountRequest,
	Error,
	GetAccountsParams,
	PageResponseArrayAccount,
	ResponseAccount
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * List accounts.
 * @summary Get a list of accounts.
 */
export const getAccounts = (params?: GetAccountsParams) => {
	return customInstance<PageResponseArrayAccount>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/accounts`,
		method: 'GET',
		params
	});
};

export const getGetAccountsQueryKey = (params?: GetAccountsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts`, ...(params ? [params] : [])] as const;
};

export const getGetAccountsQueryOptions = <
	TData = Awaited<ReturnType<typeof getAccounts>>,
	TError = ErrorType<Error>
>(
	params?: GetAccountsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getAccounts>>, TError, TData>>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAccountsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAccounts>>> = () => getAccounts(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getAccounts>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetAccountsQueryResult = NonNullable<Awaited<ReturnType<typeof getAccounts>>>;
export type GetAccountsQueryError = ErrorType<Error>;

/**
 * @summary Get a list of accounts.
 */
export const createGetAccounts = <
	TData = Awaited<ReturnType<typeof getAccounts>>,
	TError = ErrorType<Error>
>(
	params?: GetAccountsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getAccounts>>, TError, TData>>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetAccountsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Create an account by giving exchangeID and tag.
 * @summary Create an account.
 */
export const postAccounts = (createAccountRequest: BodyType<CreateAccountRequest>) => {
	return customInstance<ResponseAccount>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/accounts`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: createAccountRequest
	});
};

export const getPostAccountsMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postAccounts>>,
		TError,
		{ data: BodyType<CreateAccountRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postAccounts>>,
	TError,
	{ data: BodyType<CreateAccountRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postAccounts>>,
		{ data: BodyType<CreateAccountRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postAccounts(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostAccountsMutationResult = NonNullable<Awaited<ReturnType<typeof postAccounts>>>;
export type PostAccountsMutationBody = BodyType<CreateAccountRequest>;
export type PostAccountsMutationError = ErrorType<Error>;

/**
 * @summary Create an account.
 */
export const createPostAccounts = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postAccounts>>,
		TError,
		{ data: BodyType<CreateAccountRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postAccounts>>,
	TError,
	{ data: BodyType<CreateAccountRequest> },
	TContext
> => {
	const mutationOptions = getPostAccountsMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Fetch an account by its ID.
 * @summary Get an account.
 */
export const getAccountsAccountId = (accountId: string) => {
	return customInstance<ResponseAccount>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/accounts/${accountId}`,
		method: 'GET'
	});
};

export const getGetAccountsAccountIdQueryKey = (accountId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts/${accountId}`] as const;
};

export const getGetAccountsAccountIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getAccountsAccountId>>,
	TError = ErrorType<Error>
>(
	accountId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getAccountsAccountId>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAccountsAccountIdQueryKey(accountId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAccountsAccountId>>> = () =>
		getAccountsAccountId(accountId);

	return { queryKey, queryFn, enabled: !!accountId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getAccountsAccountId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetAccountsAccountIdQueryResult = NonNullable<
	Awaited<ReturnType<typeof getAccountsAccountId>>
>;
export type GetAccountsAccountIdQueryError = ErrorType<Error>;

/**
 * @summary Get an account.
 */
export const createGetAccountsAccountId = <
	TData = Awaited<ReturnType<typeof getAccountsAccountId>>,
	TError = ErrorType<Error>
>(
	accountId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getAccountsAccountId>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetAccountsAccountIdQueryOptions(accountId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Delete an account by its ID.
 * @summary Delete an account.
 */
export const deleteAccountsAccountId = (accountId: string) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/accounts/${accountId}`,
		method: 'DELETE'
	});
};

export const getDeleteAccountsAccountIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteAccountsAccountId>>,
		TError,
		{ accountId: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof deleteAccountsAccountId>>,
	TError,
	{ accountId: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteAccountsAccountId>>,
		{ accountId: string }
	> = (props) => {
		const { accountId } = props ?? {};

		return deleteAccountsAccountId(accountId);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteAccountsAccountIdMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteAccountsAccountId>>
>;

export type DeleteAccountsAccountIdMutationError = ErrorType<Error>;

/**
 * @summary Delete an account.
 */
export const createDeleteAccountsAccountId = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteAccountsAccountId>>,
		TError,
		{ accountId: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof deleteAccountsAccountId>>,
	TError,
	{ accountId: string },
	TContext
> => {
	const mutationOptions = getDeleteAccountsAccountIdMutationOptions(options);

	return createMutation(mutationOptions);
};
