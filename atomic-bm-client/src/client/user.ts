/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	ChangeUserPasswordRequest,
	Error,
	LoginRequest,
	ResponseArraySession,
	ResponseArrayUser,
	ResponseLoginResponse,
	ResponseUser
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * Retrieves a list of users.
 * @summary Get a list of users.
 */
export const getUsersInfo = () => {
	return customInstance<ResponseArrayUser>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/info`,
		method: 'GET'
	});
};

export const getGetUsersInfoQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/users/info`] as const;
};

export const getGetUsersInfoQueryOptions = <
	TData = Awaited<ReturnType<typeof getUsersInfo>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersInfo>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUsersInfoQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsersInfo>>> = () => getUsersInfo();

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getUsersInfo>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetUsersInfoQueryResult = NonNullable<Awaited<ReturnType<typeof getUsersInfo>>>;
export type GetUsersInfoQueryError = ErrorType<Error>;

/**
 * @summary Get a list of users.
 */
export const createGetUsersInfo = <
	TData = Awaited<ReturnType<typeof getUsersInfo>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersInfo>>, TError, TData>>;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetUsersInfoQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Authenticates a user based on email and password, generating a session token.
 * @summary User login.
 */
export const postUsersLogin = (loginRequest: BodyType<LoginRequest>) => {
	return customInstance<ResponseLoginResponse>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/login`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: loginRequest
	});
};

export const getPostUsersLoginMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postUsersLogin>>,
		TError,
		{ data: BodyType<LoginRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postUsersLogin>>,
	TError,
	{ data: BodyType<LoginRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postUsersLogin>>,
		{ data: BodyType<LoginRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postUsersLogin(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostUsersLoginMutationResult = NonNullable<Awaited<ReturnType<typeof postUsersLogin>>>;
export type PostUsersLoginMutationBody = BodyType<LoginRequest>;
export type PostUsersLoginMutationError = ErrorType<Error>;

/**
 * @summary User login.
 */
export const createPostUsersLogin = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postUsersLogin>>,
		TError,
		{ data: BodyType<LoginRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postUsersLogin>>,
	TError,
	{ data: BodyType<LoginRequest> },
	TContext
> => {
	const mutationOptions = getPostUsersLoginMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Terminates the user's session, logs them out, and disconnects any associated WebSocket connection.
 * @summary User logout.
 */
export const postUsersLogout = () => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/logout`,
		method: 'POST'
	});
};

export const getPostUsersLogoutMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postUsersLogout>>,
		TError,
		void,
		TContext
	>;
}): CreateMutationOptions<Awaited<ReturnType<typeof postUsersLogout>>, TError, void, TContext> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<Awaited<ReturnType<typeof postUsersLogout>>, void> = () => {
		return postUsersLogout();
	};

	return { mutationFn, ...mutationOptions };
};

export type PostUsersLogoutMutationResult = NonNullable<
	Awaited<ReturnType<typeof postUsersLogout>>
>;

export type PostUsersLogoutMutationError = ErrorType<Error>;

/**
 * @summary User logout.
 */
export const createPostUsersLogout = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postUsersLogout>>,
		TError,
		void,
		TContext
	>;
}): CreateMutationResult<Awaited<ReturnType<typeof postUsersLogout>>, TError, void, TContext> => {
	const mutationOptions = getPostUsersLogoutMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Retrieves details of the authenticated user.
 * @summary Get user details.
 */
export const getUsersMe = () => {
	return customInstance<ResponseUser>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/me`,
		method: 'GET'
	});
};

export const getGetUsersMeQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/users/me`] as const;
};

export const getGetUsersMeQueryOptions = <
	TData = Awaited<ReturnType<typeof getUsersMe>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersMe>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUsersMeQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsersMe>>> = () => getUsersMe();

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getUsersMe>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetUsersMeQueryResult = NonNullable<Awaited<ReturnType<typeof getUsersMe>>>;
export type GetUsersMeQueryError = ErrorType<Error>;

/**
 * @summary Get user details.
 */
export const createGetUsersMe = <
	TData = Awaited<ReturnType<typeof getUsersMe>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersMe>>, TError, TData>>;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetUsersMeQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Changes the user's password by verifying the old password,
validating the new password, and updating it in the database.
 * @summary Change user's password.
 */
export const patchUsersPassword = (
	changeUserPasswordRequest: BodyType<ChangeUserPasswordRequest>
) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/password`,
		method: 'PATCH',
		headers: { 'Content-Type': 'application/json' },
		data: changeUserPasswordRequest
	});
};

export const getPatchUsersPasswordMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof patchUsersPassword>>,
		TError,
		{ data: BodyType<ChangeUserPasswordRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof patchUsersPassword>>,
	TError,
	{ data: BodyType<ChangeUserPasswordRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof patchUsersPassword>>,
		{ data: BodyType<ChangeUserPasswordRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return patchUsersPassword(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PatchUsersPasswordMutationResult = NonNullable<
	Awaited<ReturnType<typeof patchUsersPassword>>
>;
export type PatchUsersPasswordMutationBody = BodyType<ChangeUserPasswordRequest>;
export type PatchUsersPasswordMutationError = ErrorType<Error>;

/**
 * @summary Change user's password.
 */
export const createPatchUsersPassword = <TError = ErrorType<Error>, TContext = unknown>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof patchUsersPassword>>,
		TError,
		{ data: BodyType<ChangeUserPasswordRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof patchUsersPassword>>,
	TError,
	{ data: BodyType<ChangeUserPasswordRequest> },
	TContext
> => {
	const mutationOptions = getPatchUsersPasswordMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Retrieves a list of sessions associated with the authenticated user.
 * @summary Get user active sessions.
 */
export const getUsersSessionsActive = () => {
	return customInstance<ResponseArraySession>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/active`,
		method: 'GET'
	});
};

export const getGetUsersSessionsActiveQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/active`] as const;
};

export const getGetUsersSessionsActiveQueryOptions = <
	TData = Awaited<ReturnType<typeof getUsersSessionsActive>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<
		CreateQueryOptions<Awaited<ReturnType<typeof getUsersSessionsActive>>, TError, TData>
	>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUsersSessionsActiveQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsersSessionsActive>>> = () =>
		getUsersSessionsActive();

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getUsersSessionsActive>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetUsersSessionsActiveQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUsersSessionsActive>>
>;
export type GetUsersSessionsActiveQueryError = ErrorType<Error>;

/**
 * @summary Get user active sessions.
 */
export const createGetUsersSessionsActive = <
	TData = Awaited<ReturnType<typeof getUsersSessionsActive>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<
		CreateQueryOptions<Awaited<ReturnType<typeof getUsersSessionsActive>>, TError, TData>
	>;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetUsersSessionsActiveQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Terminates all sessions associated with the authenticated user, logging them out,
and disconnecting any associated WebSocket connections.
 * @summary Delete all user sessions.
 */
export const postUsersSessionsBulkDelete = () => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/bulkDelete`,
		method: 'POST'
	});
};

export const getPostUsersSessionsBulkDeleteMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postUsersSessionsBulkDelete>>,
		TError,
		void,
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postUsersSessionsBulkDelete>>,
	TError,
	void,
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postUsersSessionsBulkDelete>>,
		void
	> = () => {
		return postUsersSessionsBulkDelete();
	};

	return { mutationFn, ...mutationOptions };
};

export type PostUsersSessionsBulkDeleteMutationResult = NonNullable<
	Awaited<ReturnType<typeof postUsersSessionsBulkDelete>>
>;

export type PostUsersSessionsBulkDeleteMutationError = ErrorType<Error>;

/**
 * @summary Delete all user sessions.
 */
export const createPostUsersSessionsBulkDelete = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postUsersSessionsBulkDelete>>,
		TError,
		void,
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postUsersSessionsBulkDelete>>,
	TError,
	void,
	TContext
> => {
	const mutationOptions = getPostUsersSessionsBulkDeleteMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Deletes a user session identified by the provided session ID.
 * @summary Delete a user session.
 */
export const deleteUsersSessionsSessionId = (sessionId: number) => {
	return customInstance<void>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/${sessionId}`,
		method: 'DELETE'
	});
};

export const getDeleteUsersSessionsSessionIdMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteUsersSessionsSessionId>>,
		TError,
		{ sessionId: number },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof deleteUsersSessionsSessionId>>,
	TError,
	{ sessionId: number },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteUsersSessionsSessionId>>,
		{ sessionId: number }
	> = (props) => {
		const { sessionId } = props ?? {};

		return deleteUsersSessionsSessionId(sessionId);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteUsersSessionsSessionIdMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteUsersSessionsSessionId>>
>;

export type DeleteUsersSessionsSessionIdMutationError = ErrorType<Error>;

/**
 * @summary Delete a user session.
 */
export const createDeleteUsersSessionsSessionId = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteUsersSessionsSessionId>>,
		TError,
		{ sessionId: number },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof deleteUsersSessionsSessionId>>,
	TError,
	{ sessionId: number },
	TContext
> => {
	const mutationOptions = getDeleteUsersSessionsSessionIdMutationOptions(options);

	return createMutation(mutationOptions);
};
