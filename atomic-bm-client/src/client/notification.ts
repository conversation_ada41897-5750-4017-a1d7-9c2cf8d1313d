/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createMutation, createQuery } from '@tanstack/svelte-query';
import type {
	CreateMutationOptions,
	CreateMutationResult,
	CreateQueryOptions,
	CreateQueryResult,
	MutationFunction,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type {
	CreateNotificationSubscriptionRequest,
	CreatePushNotificationRequest,
	DeleteNotificationSubscriptionRequest,
	Error,
	GetNotificationsPushParams,
	GetNotificationsSubscribeParams,
	PageResponseArrayNotificationSubscription,
	PageResponseArrayPushNotification,
	ResponseArrayNotificationSubscription,
	ResponsePushNotification
} from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType, BodyType } from '../utils/api-mutator';

/**
 * List registered push notifications of a user with optional filtering and ordering.
 * @summary List push notifications.
 */
export const getNotificationsPush = (params?: GetNotificationsPushParams) => {
	return customInstance<PageResponseArrayPushNotification>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push`,
		method: 'GET',
		params
	});
};

export const getGetNotificationsPushQueryKey = (params?: GetNotificationsPushParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push`,
		...(params ? [params] : [])
	] as const;
};

export const getGetNotificationsPushQueryOptions = <
	TData = Awaited<ReturnType<typeof getNotificationsPush>>,
	TError = ErrorType<Error>
>(
	params?: GetNotificationsPushParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsPush>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetNotificationsPushQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getNotificationsPush>>> = () =>
		getNotificationsPush(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getNotificationsPush>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetNotificationsPushQueryResult = NonNullable<
	Awaited<ReturnType<typeof getNotificationsPush>>
>;
export type GetNotificationsPushQueryError = ErrorType<Error>;

/**
 * @summary List push notifications.
 */
export const createGetNotificationsPush = <
	TData = Awaited<ReturnType<typeof getNotificationsPush>>,
	TError = ErrorType<Error>
>(
	params?: GetNotificationsPushParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsPush>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetNotificationsPushQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Register or update a push notification device by providing the device token and platform.
 * @summary Register or update a push notification device.
 */
export const postNotificationsPush = (
	createPushNotificationRequest: BodyType<CreatePushNotificationRequest>
) => {
	return customInstance<ResponsePushNotification>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: createPushNotificationRequest
	});
};

export const getPostNotificationsPushMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postNotificationsPush>>,
		TError,
		{ data: BodyType<CreatePushNotificationRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postNotificationsPush>>,
	TError,
	{ data: BodyType<CreatePushNotificationRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postNotificationsPush>>,
		{ data: BodyType<CreatePushNotificationRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postNotificationsPush(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostNotificationsPushMutationResult = NonNullable<
	Awaited<ReturnType<typeof postNotificationsPush>>
>;
export type PostNotificationsPushMutationBody = BodyType<CreatePushNotificationRequest>;
export type PostNotificationsPushMutationError = ErrorType<Error>;

/**
 * @summary Register or update a push notification device.
 */
export const createPostNotificationsPush = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postNotificationsPush>>,
		TError,
		{ data: BodyType<CreatePushNotificationRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postNotificationsPush>>,
	TError,
	{ data: BodyType<CreatePushNotificationRequest> },
	TContext
> => {
	const mutationOptions = getPostNotificationsPushMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Delete a push notification device by providing the device token.
 * @summary Delete a push notification device.
 */
export const deleteNotificationsPushDeviceToken = (deviceToken: string) => {
	return customInstance<string>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push/${deviceToken}`,
		method: 'DELETE'
	});
};

export const getDeleteNotificationsPushDeviceTokenMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteNotificationsPushDeviceToken>>,
		TError,
		{ deviceToken: string },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof deleteNotificationsPushDeviceToken>>,
	TError,
	{ deviceToken: string },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof deleteNotificationsPushDeviceToken>>,
		{ deviceToken: string }
	> = (props) => {
		const { deviceToken } = props ?? {};

		return deleteNotificationsPushDeviceToken(deviceToken);
	};

	return { mutationFn, ...mutationOptions };
};

export type DeleteNotificationsPushDeviceTokenMutationResult = NonNullable<
	Awaited<ReturnType<typeof deleteNotificationsPushDeviceToken>>
>;

export type DeleteNotificationsPushDeviceTokenMutationError = ErrorType<Error>;

/**
 * @summary Delete a push notification device.
 */
export const createDeleteNotificationsPushDeviceToken = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof deleteNotificationsPushDeviceToken>>,
		TError,
		{ deviceToken: string },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof deleteNotificationsPushDeviceToken>>,
	TError,
	{ deviceToken: string },
	TContext
> => {
	const mutationOptions = getDeleteNotificationsPushDeviceTokenMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * List notification subscriptions with optional filtering and ordering.
 * @summary List notification subscriptions.
 */
export const getNotificationsSubscribe = (params?: GetNotificationsSubscribeParams) => {
	return customInstance<PageResponseArrayNotificationSubscription>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe`,
		method: 'GET',
		params
	});
};

export const getGetNotificationsSubscribeQueryKey = (params?: GetNotificationsSubscribeParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe`,
		...(params ? [params] : [])
	] as const;
};

export const getGetNotificationsSubscribeQueryOptions = <
	TData = Awaited<ReturnType<typeof getNotificationsSubscribe>>,
	TError = ErrorType<Error>
>(
	params?: GetNotificationsSubscribeParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsSubscribe>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetNotificationsSubscribeQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getNotificationsSubscribe>>> = () =>
		getNotificationsSubscribe(params);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getNotificationsSubscribe>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetNotificationsSubscribeQueryResult = NonNullable<
	Awaited<ReturnType<typeof getNotificationsSubscribe>>
>;
export type GetNotificationsSubscribeQueryError = ErrorType<Error>;

/**
 * @summary List notification subscriptions.
 */
export const createGetNotificationsSubscribe = <
	TData = Awaited<ReturnType<typeof getNotificationsSubscribe>>,
	TError = ErrorType<Error>
>(
	params?: GetNotificationsSubscribeParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsSubscribe>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetNotificationsSubscribeQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Create a new notification subscription by specifying the event type and resource ID.
 * @summary Create a notification subscription.
 */
export const postNotificationsSubscribe = (
	createNotificationSubscriptionRequest: BodyType<CreateNotificationSubscriptionRequest>
) => {
	return customInstance<ResponseArrayNotificationSubscription>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: createNotificationSubscriptionRequest
	});
};

export const getPostNotificationsSubscribeMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postNotificationsSubscribe>>,
		TError,
		{ data: BodyType<CreateNotificationSubscriptionRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postNotificationsSubscribe>>,
	TError,
	{ data: BodyType<CreateNotificationSubscriptionRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postNotificationsSubscribe>>,
		{ data: BodyType<CreateNotificationSubscriptionRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postNotificationsSubscribe(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostNotificationsSubscribeMutationResult = NonNullable<
	Awaited<ReturnType<typeof postNotificationsSubscribe>>
>;
export type PostNotificationsSubscribeMutationBody =
	BodyType<CreateNotificationSubscriptionRequest>;
export type PostNotificationsSubscribeMutationError = ErrorType<Error>;

/**
 * @summary Create a notification subscription.
 */
export const createPostNotificationsSubscribe = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postNotificationsSubscribe>>,
		TError,
		{ data: BodyType<CreateNotificationSubscriptionRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postNotificationsSubscribe>>,
	TError,
	{ data: BodyType<CreateNotificationSubscriptionRequest> },
	TContext
> => {
	const mutationOptions = getPostNotificationsSubscribeMutationOptions(options);

	return createMutation(mutationOptions);
};
/**
 * Delete notification subscriptions by providing an array of subscription IDs.
 * @summary Delete notification subscriptions.
 */
export const postNotificationsSubscribeBulkDelete = (
	deleteNotificationSubscriptionRequest: BodyType<DeleteNotificationSubscriptionRequest>
) => {
	return customInstance<string>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe/bulkDelete`,
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		data: deleteNotificationSubscriptionRequest
	});
};

export const getPostNotificationsSubscribeBulkDeleteMutationOptions = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postNotificationsSubscribeBulkDelete>>,
		TError,
		{ data: BodyType<DeleteNotificationSubscriptionRequest> },
		TContext
	>;
}): CreateMutationOptions<
	Awaited<ReturnType<typeof postNotificationsSubscribeBulkDelete>>,
	TError,
	{ data: BodyType<DeleteNotificationSubscriptionRequest> },
	TContext
> => {
	const { mutation: mutationOptions } = options ?? {};

	const mutationFn: MutationFunction<
		Awaited<ReturnType<typeof postNotificationsSubscribeBulkDelete>>,
		{ data: BodyType<DeleteNotificationSubscriptionRequest> }
	> = (props) => {
		const { data } = props ?? {};

		return postNotificationsSubscribeBulkDelete(data);
	};

	return { mutationFn, ...mutationOptions };
};

export type PostNotificationsSubscribeBulkDeleteMutationResult = NonNullable<
	Awaited<ReturnType<typeof postNotificationsSubscribeBulkDelete>>
>;
export type PostNotificationsSubscribeBulkDeleteMutationBody =
	BodyType<DeleteNotificationSubscriptionRequest>;
export type PostNotificationsSubscribeBulkDeleteMutationError = ErrorType<Error>;

/**
 * @summary Delete notification subscriptions.
 */
export const createPostNotificationsSubscribeBulkDelete = <
	TError = ErrorType<Error>,
	TContext = unknown
>(options?: {
	mutation?: CreateMutationOptions<
		Awaited<ReturnType<typeof postNotificationsSubscribeBulkDelete>>,
		TError,
		{ data: BodyType<DeleteNotificationSubscriptionRequest> },
		TContext
	>;
}): CreateMutationResult<
	Awaited<ReturnType<typeof postNotificationsSubscribeBulkDelete>>,
	TError,
	{ data: BodyType<DeleteNotificationSubscriptionRequest> },
	TContext
> => {
	const mutationOptions = getPostNotificationsSubscribeBulkDeleteMutationOptions(options);

	return createMutation(mutationOptions);
};
