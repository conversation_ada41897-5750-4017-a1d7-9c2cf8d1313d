/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.12.0
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import type { Error, PageResponseArrayRegion, ResponseRegion } from './api.schemas';
import { customInstance } from '../utils/api-mutator';
import type { ErrorType } from '../utils/api-mutator';

/**
 * List regions.
 * @summary Fetch a list of regions.
 */
export const getRegions = () => {
	return customInstance<PageResponseArrayRegion>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/regions`,
		method: 'GET'
	});
};

export const getGetRegionsQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/regions`] as const;
};

export const getGetRegionsQueryOptions = <
	TData = Awaited<ReturnType<typeof getRegions>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getRegions>>, TError, TData>>;
}) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetRegionsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getRegions>>> = () => getRegions();

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getRegions>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetRegionsQueryResult = NonNullable<Awaited<ReturnType<typeof getRegions>>>;
export type GetRegionsQueryError = ErrorType<Error>;

/**
 * @summary Fetch a list of regions.
 */
export const createGetRegions = <
	TData = Awaited<ReturnType<typeof getRegions>>,
	TError = ErrorType<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getRegions>>, TError, TData>>;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetRegionsQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Fetch a region by its id.
 * @summary Fetch a region by its id.
 */
export const getRegionsRegionId = (regionId: string) => {
	return customInstance<ResponseRegion>({
		url: `${import.meta.env.VITE_ATOMIC_BASEURL}/regions/${regionId}`,
		method: 'GET'
	});
};

export const getGetRegionsRegionIdQueryKey = (regionId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/regions/${regionId}`] as const;
};

export const getGetRegionsRegionIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getRegionsRegionId>>,
	TError = ErrorType<Error>
>(
	regionId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getRegionsRegionId>>, TError, TData>
		>;
	}
) => {
	const { query: queryOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetRegionsRegionIdQueryKey(regionId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getRegionsRegionId>>> = () =>
		getRegionsRegionId(regionId);

	return { queryKey, queryFn, enabled: !!regionId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getRegionsRegionId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetRegionsRegionIdQueryResult = NonNullable<
	Awaited<ReturnType<typeof getRegionsRegionId>>
>;
export type GetRegionsRegionIdQueryError = ErrorType<Error>;

/**
 * @summary Fetch a region by its id.
 */
export const createGetRegionsRegionId = <
	TData = Awaited<ReturnType<typeof getRegionsRegionId>>,
	TError = ErrorType<Error>
>(
	regionId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getRegionsRegionId>>, TError, TData>
		>;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetRegionsRegionIdQueryOptions(regionId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};
