import type { LayoutLoad } from './$types';
import { checkAuth, getStorage } from '$utils';
import { browser } from '$app/environment';
import { getUsersInfo } from '../../client/user';

export const load = (async ({ depends, parent }) => {
	await parent();
	depends('navigation:navigation');
	if (browser) {
		checkAuth();
		const users = await getUsersInfo();

		return {
			users: users.data
		};
	}
}) satisfies LayoutLoad;
