<script lang="ts">
	import type { GetAccountsParams } from '$client/api.schemas';
	import { createGetAccounts, createDeleteAccountsAccountId } from '$client/account';
	import Input from '$lib/components/ui/input.svelte';
	import BaseFilters from '$lib/components/base-filters.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import BaseTable from '$lib/components/base-table.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
	import ConfirmModal from '$lib/components/confirm-modal.svelte';
	import BaseLoading from '$lib/components/base-loading.svelte';
	import { appStore } from '$stores/app-store';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';
	import {
		getStorage,
		saveStorage,
		clearApp,
		tabNavigate,
		persistData,
		getPersistData,
		formatCommonDate,
		toast,
		handleErr,
		checkObjectKeys,
		clearPersistData
	} from '$utils';
	import { browser } from '$app/environment';

	let currentPage = $state(1);
	let deletingAccount = $state(false);
	let selectedAccountID = $state('');
	let searchAccounts = $state('');
	let filters: GetAccountsParams = $state({
		exchangeId: undefined,
		tag: undefined,
		isDeleted: undefined,
		orderBy: undefined,
		orderDirection: undefined,
		limit: undefined,
		offset: undefined
	});

	$effect(() => {
		if (browser) {
			const storageValue = getStorage(page.params.tab_id as string);
			if (storageValue) {
				searchAccounts = storageValue;
			} else {
				searchAccounts = '';
			}
			filters.limit = getPersistData('limitaccount') ?? undefined;
			filters.orderDirection = getPersistData('directionaccount') ?? undefined;
			filters.orderBy = getPersistData('orderbyaccount') ?? undefined;
			filters.isDeleted = getPersistData('isdeletedaccount') ?? undefined;
		}
	});

	const accountsQuery = $derived(
		createGetAccounts(filters, {
			query: {
				retry: false
			}
		})
	);

	const deleteAccountMutation = $derived(
		createDeleteAccountsAccountId({
			mutation: {
				onSuccess: () => {
					$accountsQuery.refetch();
					toast('Account deleted successfully', 'success');
					deletingAccount = false;
					selectedAccountID = '';
				},
				onError: (error) => {
					handleErr(error);
					deletingAccount = false;
				}
			}
		})
	);

	const accountList = $derived($accountsQuery.data?.data);
	const filteredAccounts = $derived(
		accountList?.filter((acc) => {
			if (searchAccounts) {
				return (
					acc.tag?.toLowerCase().includes(searchAccounts.toLowerCase()) ||
					acc.id?.toLowerCase().includes(searchAccounts.toLowerCase())
				);
			}
			return acc;
		})
	);

	$effect(() => {
		if ($accountsQuery.isError && $accountsQuery.error?.status === 403) {
			clearApp();
		}
	});

	$effect(() => {
		if ($accountsQuery.failureReason?.response?.status === 403) {
			clearApp();
		}
	});

	async function saveInputValue(e: Event) {
		const target = e.target as HTMLInputElement;
		searchAccounts = target.value;
		await tick();
		saveStorage(page.params.tab_id as string, target.value);
	}

	async function confirmDeleteAccount() {
		if (!selectedAccountID) return;

		try {
			deletingAccount = true;
			await $deleteAccountMutation.mutateAsync({ accountId: selectedAccountID });
		} catch (error) {
			console.error('Delete failed:', error);
		}
	}

	async function handlePagination(type: 'prev' | 'next') {
		if (type === 'next' && filters.offset === undefined && filters.limit === undefined) {
			filters.limit = 10;
			filters.offset = 10;
			currentPage++;
			return;
		}

		if (type === 'next' && filters.offset === 0 && filters.limit) {
			filters.offset += filters.limit;
			currentPage++;
			return;
		}

		if (filters.offset && filters.limit) {
			if (type === 'next') {
				filters.offset += filters.limit;
				currentPage++;
			} else if (type === 'prev') {
				filters.offset -= filters.limit;
				currentPage--;
			}

			$accountsQuery.refetch();
		}
	}

	async function setFilterOptions(e: CustomEvent) {
		e.detail.exchangeId &&
			(await persistData(undefined, 'accountidaccount', true, e.detail.exchangeId));
		e.detail.tag && (await persistData(undefined, 'tagaccount', true, e.detail.tag));
		filters.exchangeId = e.detail.accountId;
		filters.tag = e.detail.tag;
	}

	async function handleFilterSelection(e: CustomEvent, filterType: string) {
		switch (filterType) {
			case 'limit':
				await persistData(undefined, 'limitaccount', true, e.detail);
				filters.limit = e.detail;
				break;
			case 'direction':
				await persistData(undefined, 'directionaccount', true, e.detail);
				filters.orderDirection = e.detail;
				break;
			case 'orderBy':
				await persistData(undefined, 'orderbyaccount', true, e.detail);
				filters.orderBy = e.detail;
				break;
			case 'isDeleted':
				await persistData(undefined, 'isdeletedaccount', true, e.detail);
				filters.isDeleted = e.detail;
				break;
			case 'options':
				setFilterOptions(e);
				break;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'Accounts';
	});
</script>

<svelte:head>
	<title>Accounts | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full flex-col p-1.5">
	<div class="flex justify-between">
		<div class="flex flex-1 flex-wrap items-end gap-2">
			<div>
				<Input
					placeholder="Search for an account"
					on:input={saveInputValue}
					value={searchAccounts}
				/>
			</div>
			<BaseFilters
				filterType="accounts"
				tag={filters.tag}
				pageLimit={filters.limit}
				directionOption={filters.orderDirection}
				accountsOrderBy={filters.orderBy}
				activeState={filters.isDeleted}
				exchangeId={filters.exchangeId}
				on:pageLimit={(e) => handleFilterSelection(e, 'limit')}
				on:direction={(e) => handleFilterSelection(e, 'direction')}
				on:accountsOrderBy={(e) => handleFilterSelection(e, 'orderBy')}
				on:activeState={(e) => handleFilterSelection(e, 'isDeleted')}
				on:filterOptions={(e) => handleFilterSelection(e, 'options')}
				on:refresh={() => $accountsQuery.refetch()}
			/>
		</div>
		<div>
			<Button on:click={() => tabNavigate('New account', '/accounts/create')}>New account</Button>
		</div>
	</div>
	<div class="mt-2 flex-1">
		{#if checkObjectKeys(filters)}
			<div class="mb-2 mt-1.5 flex max-w-fit items-center gap-2 px-1">
				<h2 class="text-[0.65rem] font-semibold">Active filters:</h2>
				<div class="flex gap-1">
					{#if filters.exchangeId}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.exchangeId = undefined;
								clearPersistData('accountidaccount');
							}}>Account</Button
						>
					{/if}
					{#if filters.isDeleted}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.isDeleted = undefined;
								clearPersistData('isdeletedaccount');
							}}
							>{#if filters.isDeleted}Deleted{:else}Active{/if}</Button
						>
					{/if}
					{#if filters.orderBy}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderBy = undefined;
								clearPersistData('orderbyaccount');
							}}>Order by: <strong>{filters.orderBy}</strong></Button
						>
					{/if}
					{#if filters.orderDirection}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderDirection = undefined;
								clearPersistData('orderaccount');
							}}>Order direction: <strong>{filters.orderDirection}</strong></Button
						>
					{/if}
					{#if filters.limit && filters.limit > 10}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={() => {
								filters.limit = undefined;
								clearPersistData('limitaccount');
							}}>Limit: <strong>{filters.limit}</strong></Button
						>
					{/if}
				</div>
			</div>
		{/if}
		{#if $accountsQuery.isLoading}
			<BaseLoading title="Loading accounts data..." />
		{:else if $accountsQuery.isError}
			<div class="flex h-full flex-col items-center justify-center">
				<h1 class="text-2xl font-semibold">Failed to load accounts</h1>
				{$accountsQuery.failureReason?.message}
				<Button class="mt-2" on:click={() => $accountsQuery.refetch()}>Retry</Button>
			</div>
		{:else}
			<BaseTable
				title="Accounts list"
				name="Account"
				hideDetails={true}
				data={filteredAccounts}
				dataHeaders={[
					{
						title: 'Tag',
						class: 'w-[100px]'
					},
					{
						title: 'ID'
					},
					{
						title: 'Updated at'
					},
					{
						title: 'Created at'
					}
				]}
				on:delete={(e) => {
					deletingAccount = true;
					selectedAccountID = e.detail.id;
				}}
				{currentPage}
				pageLength={filteredAccounts?.length}
				filters={{
					limit: filters.limit,
					offset: filters.offset
				}}
				on:next={() => handlePagination('next')}
				on:prev={() => handlePagination('prev')}
			>
				{#snippet children({ item, TableCell })}
					<TableCell>{item.tag}</TableCell>
					<TableCell
						><div class="flex items-center gap-1">
							{item.id}
							<Icon icon="mingcute:copy-line" />
						</div></TableCell
					>
					<TableCell>{formatCommonDate(item.updatedAt)}</TableCell>
					<TableCell>{formatCommonDate(item.createdAt)}</TableCell>
				{/snippet}
			</BaseTable>
		{/if}
	</div>
</section>

<ConfirmModal
	open={deletingAccount}
	title="Delete account"
	description="Are you sure you want to delete this account? This action cannot be undone."
	on:openChange={(e) => {
		deletingAccount = e.detail;
		if (!deletingAccount) selectedAccountID = '';
	}}
	on:cancel={() => (deletingAccount = false)}
	on:confirm={confirmDeleteAccount}
/>
