<script lang="ts">
	import { page } from '$app/state';
	import { appStore } from '$stores/app-store';
	import * as Card from '$lib/components/ui/card';
	import Input from '$lib/components/ui/input.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import Icon from '$lib/components/ui/icon.svelte';
	import { onMount } from 'svelte';
	import {
		tabNavigate,
		getPersistData,
		persistData,
		clearPersistData,
		handleErr,
		toast
	} from '$utils';
	import { browser } from '$app/environment';
	import { createPostAccounts } from '$client/account';
	import { createGetExchanges } from '$client/exchange';
	import { z } from 'zod';

	let exchange = $state({
		label: '',
		value: ''
	});
	let region = $state('');
	let tag = $state('');
	let open = $state(false);
	let isSubmitting = $state(false);
	let formErrors = $state<Record<string, string>>({});

	const exchangeSchema = z.object({
		label: z.string().min(1, 'Exchange is required'),
		value: z.string().min(1, 'Exchange is required')
	});

	const regionSchema = z
		.string()
		.min(1, 'Region is required')
		.max(50, 'Region must be 50 characters or less');

	const tagSchema = z
		.string()
		.min(1, 'Tag is required')
		.max(20, 'Tag must be 20 characters or less')
		.regex(/^[a-zA-Z0-9-_]+$/, 'Tag can only contain letters, numbers, hyphens and underscores');

	const formSchema = z.object({
		exchange: exchangeSchema,
		region: regionSchema,
		tag: tagSchema
	});

	$effect(() => {
		if (browser && page) {
			const exchangeFromPersist = getPersistData('newaccountexchange');
			if (exchangeFromPersist) {
				exchange = exchangeFromPersist;
			} else {
				exchange = { label: '', value: '' };
			}
			region = getPersistData('newaccountregion') || '';
			tag = getPersistData('newaccounttag') || '';
		}
	});

	function clearFieldsData() {
		clearPersistData('newaccountexchange');
		clearPersistData('newaccountregion');
		clearPersistData('newaccounttag');
	}

	function returnToAccounts() {
		clearFieldsData();
		const isReturnToBots = page.url.searchParams.get('returnToBots');
		if (!isReturnToBots) {
			tabNavigate('Accounts', '/accounts');
		} else {
			tabNavigate('New bot', '/bots/create');
		}
	}

	const exchangesQuery = $derived(
		createGetExchanges(undefined, {
			query: {
				retry: false
			}
		})
	);

	const createAccountMutation = $derived(
		createPostAccounts({
			mutation: {
				onSuccess: () => {
					toast('Account created successfully', 'success');
					const isReturnToBots = page.url.searchParams.get('returnToBots');
					if (!isReturnToBots) {
						tabNavigate('Accounts', '/accounts');
					} else {
						tabNavigate(
							'New bot',
							`/bots/create?accInfo=["${exchange.label}","${exchange.value}"]`
						);
					}
					clearFieldsData();
				},
				onError: (error) => {
					handleErr(error);
				}
			}
		})
	);

	const exchangeOptions = $derived(
		$exchangesQuery.data?.data?.map((exchange) => ({
			label: exchange.name,
			value: exchange.exchangeId
		})) || []
	);

	const selectedExchangeLabel = $derived(
		exchangeOptions?.find((ex) => ex.value === exchange.value)?.label || ''
	);

	function closeCombobox() {
		open = false;
	}

	async function selectExchange(selectedExchange: { label: string; value: string }) {
		if (formErrors.exchange) {
			const { exchange, ...rest } = formErrors;
			formErrors = rest;
		}

		await persistData(undefined, 'newaccountexchange', true, selectedExchange);
		exchange = selectedExchange;
		closeCombobox();
	}

	function validateForm(): { isValid: boolean; errors: Record<string, string> } {
		const formData = {
			exchange: {
				label: exchange.label,
				value: exchange.value
			},
			region: region.trim(),
			tag: tag.trim()
		};

		try {
			formSchema.parse(formData);
			return { isValid: true, errors: {} };
		} catch (error) {
			if (error instanceof z.ZodError) {
				const errors: Record<string, string> = {};
				const zodError = error as z.ZodError;

				zodError.issues.forEach((issue) => {
					const path = issue.path.join('.');
					if (path === 'exchange.label' || path === 'exchange.value') {
						errors.exchange = issue.message;
					} else if (path === 'region') {
						errors.region = issue.message;
					} else if (path === 'tag') {
						errors.tag = issue.message;
					}
				});

				return { isValid: false, errors };
			}
			return { isValid: false, errors: { general: 'Validation failed' } };
		}
	}

	async function confirmCreateAccount() {
		formErrors = {};

		const validation = validateForm();

		if (!validation.isValid) {
			formErrors = validation.errors;
			return;
		}

		try {
			isSubmitting = true;
			await $createAccountMutation.mutateAsync({
				data: {
					exchangeId: exchange.value,
					regionId: region.trim(),
					tag: tag.trim()
				}
			});
		} catch (error) {
			console.error('Create account failed:', error);
		} finally {
			isSubmitting = false;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'New account';
	});
</script>

<svelte:head>
	<title>New account | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={confirmCreateAccount}>
		<Card.Header title="New account" on:iconClick={returnToAccounts} />
		<Card.Body class="space-y-4">
			<div class="flex w-full flex-col gap-1.5">
				<div class="flex flex-col">
					<span class="text-xs opacity-90">
						Exchange
						<span class="text-xs text-red-900">*</span>
					</span>
					<span class="text-[0.64rem] opacity-70"
						>Select the exchange that will be linked to the account</span
					>
				</div>

				<Popover.Root bind:open>
					<Popover.Trigger class="w-full">
						<Button
							class="!w-full justify-between border-gray-800 bg-dark-7 text-xs text-white hover:border-gray-700 focus:border-gray-600"
							role="combobox"
							aria-expanded={open}
							disabled={isSubmitting}
						>
							<span class="truncate">
								{selectedExchangeLabel || 'Select an exchange'}
							</span>
							<Icon
								icon="ph:caret-down"
								class="ml-2 opacity-50 transition-transform {open ? 'rotate-180' : ''}"
							/>
						</Button>
					</Popover.Trigger>
					<Popover.Content class="w-[360px] border border-gray-800 bg-dark-9 p-0 shadow-lg">
						<Command.Root class="border-0 bg-dark-9 text-white">
							<Command.Input
								placeholder="Search exchanges..."
								class="border-b border-gray-800 bg-dark-9 text-xs text-white placeholder:text-gray-500"
							/>
							<Command.List class="max-h-[200px] bg-dark-9">
								<Command.Empty class="py-4 text-xs text-gray-200">No exchange found.</Command.Empty>
								<Command.Group>
									{#each exchangeOptions || [] as exchangeOption (exchangeOption.value)}
										<Command.Item
											value={exchangeOption.value}
											onSelect={() => selectExchange(exchangeOption)}
											class="text-whitre flex cursor-pointer items-center gap-2 bg-dark-8 px-3 py-2 text-xs text-white hover:bg-dark-6 aria-selected:bg-dark-6 aria-selected:text-white"
										>
											<span class="truncate capitalize">{exchangeOption.label}</span>
										</Command.Item>
									{/each}
								</Command.Group>
							</Command.List>
						</Command.Root>
					</Popover.Content>
				</Popover.Root>

				{#if formErrors.exchange}
					<div class="ml-1 mt-1 text-xs text-red-500">
						{formErrors.exchange}
					</div>
				{/if}
			</div>

			<Input
				id="region"
				required
				label="Region"
				description="The region where the account will operate (e.g., us-east-1, eu-west-1)"
				placeholder="us-east-1"
				on:input={async (e) => {
					if (formErrors.region) {
						const { region, ...rest } = formErrors;
						formErrors = rest;
					}

					region = await persistData(e, 'newaccountregion');
				}}
				value={region}
				disabled={isSubmitting}
			/>

			{#if formErrors.region}
				<div class="ml-1 mt-1 text-xs text-red-500">
					{formErrors.region}
				</div>
			{/if}

			<Input
				id="tag"
				required
				label="Tag"
				description="The tag of the account (letters, numbers, hyphens and underscores only)"
				placeholder="Insert a Tag"
				on:input={async (e) => {
					if (formErrors.tag) {
						const { tag, ...rest } = formErrors;
						formErrors = rest;
					}

					tag = await persistData(e, 'newaccounttag');
				}}
				value={tag}
				disabled={isSubmitting}
			/>

			{#if formErrors.tag}
				<div class="ml-1 mt-1 text-xs text-red-500">
					{formErrors.tag}
				</div>
			{/if}
		</Card.Body>
		<Card.Footer
			on:cancel={returnToAccounts}
			confirmBtnType="submit"
			confirmText={isSubmitting ? 'Creating...' : 'Create Account'}
		/>
	</Card.Main>
</section>
