<script lang="ts">
	import { run } from 'svelte/legacy';

	import { invalidate } from '$app/navigation';
	import { browser } from '$app/environment';
	import { page } from '$app/stores';
	import * as Card from '$lib/components/ui/card';
	import Input from '$lib/components/ui/input.svelte';
	import Searcher from '$lib/components/ui/searcher.svelte';
	import {
		clearPersistData,
		getPersistData,
		getStorage,
		handleErr,
		persistData,
		tabNavigate,
		toast
	} from '$utils';
	import { patchBotsBotId } from '$client/bot';
	import { onMount } from 'svelte';

	let { data } = $props();

	let account = $state({
		label: '',
		value: ''
	});
	let symbol = $state('');
	let region = $state('');
	let tag = $state('');

	run(() => {
		if (browser && $page) {
			const accountFromPersist = getPersistData('editbotaccount');
			if (accountFromPersist) {
				account = accountFromPersist;
			} else {
				account = { label: '', value: '' };
			}
			symbol = getPersistData('editbotsymbol');
			region = getPersistData('editbotregion');
			tag = getPersistData('editbottag');
		}
	});

	let accountOptions = $derived(
		data.accounts?.data.data.map((account) => {
			return { label: account.exchangeId, value: account.id };
		})
	);

	function loadBotInfo() {
		const selectedAccount = data.accounts?.data.data.find((account: any) => {
			return account.id === data.bot?.data.data.accountId;
		});
		if (selectedAccount) {
			account.label = selectedAccount.exchangeId;
			account.value = selectedAccount.id;
		}
		symbol = data.bot?.data.data.symbol as string;
		tag = data.bot?.data.data.tag as string;
	}

	function clearFieldsData() {
		clearPersistData('editbotaccount');
		clearPersistData('editbotsymbol');
		clearPersistData('editbotregion');
		clearPersistData('editbottag');
	}

	function returnToBots() {
		const keepBot = $page.url.searchParams.get('keepBot');
		clearFieldsData();

		if (!keepBot) {
			tabNavigate('Bots', '/bots');
		} else {
			tabNavigate('', `/bots/${$page.params.bot_id}`, false, true);
		}
	}

	async function confirmUpdateBot() {
		try {
			await patchBotsBotId(
				$page.params.bot_id as string,
				{
					accountId: account.value,
					symbol,
					tag
				},
				{
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			);

			clearFieldsData();
			toast('Bot updated', 'success');
			const keepBot = $page.url.searchParams.get('keepBot');

			if (!keepBot) {
				tabNavigate('Bots', '/bots');
			} else {
				await invalidate('reloadbot:reloadbot');
				tabNavigate(`${account.label} / ${region}`, `/bots/${$page.params.bot_id}`, false);
			}
		} catch (error) {
			handleErr(error);
		}
	}

	onMount(() => {
		loadBotInfo();
	});
</script>

<svelte:head>
	<title>Edit bot | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={confirmUpdateBot}>
		<Card.Header title="Edit bot" on:iconClick={returnToBots} />
		<Card.Body class="space-y-2">
			<Searcher
				options={accountOptions}
				defaultSelected={account}
				hasSelected={account.label !== '' && account.value !== ''}
				id="account"
				required
				label="Account"
				description="Every bot must be linked to an account"
				placeholder="Select an account"
				on:selected={async (e) => {
					await persistData(undefined, 'editbotaccount', true, e.detail);
					account = e.detail;
				}}
			/>
			<Input
				id="symbol"
				required
				label="Symbol"
				description="The symbol of the bot"
				placeholder="Insert a Symbol"
				on:input={async (e) => {
					symbol = await persistData(e, 'editbotsymbol');
				}}
				value={symbol}
			/>
			<Input
				id="region"
				required
				label="Region"
				description="The region of the bot"
				placeholder="Insert a Region"
				on:input={async (e) => {
					region = await persistData(e, 'editbotregion');
				}}
				value={region}
			/>
			<Input
				id="tag"
				required
				label="Tag"
				description="The tag of the bot"
				placeholder="Insert a Tag"
				on:input={async (e) => {
					tag = await persistData(e, 'editbottag');
				}}
				value={tag}
			/>
		</Card.Body>
		<Card.Footer on:cancel={returnToBots} confirmBtnType="submit" />
	</Card.Main>
</section>
