<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	//@ts-nocheck
	// wait until the param returned keys are fixed on the backend.

	import { flip } from 'svelte/animate';
	import type { ParamRequest } from '$client/api.schemas';
	import { page } from '$app/stores';
	import { createGetParams, postParams, postParamsBulkDelete } from '$client/params';
	import { appStore } from '$stores/app-store';
	import { getStorage, handleErr, toast } from '$utils';
	import { onMount } from 'svelte';
	import BaseModal from '$lib/components/base-modal.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import Input from '$lib/components/ui/input.svelte';
	import * as Card from '$lib/components/ui/card';
	import BaseLoading from '$lib/components/base-loading.svelte';

	let params = $derived(
		createGetParams(
			{
				scope: ['bot'],
				scopeId: [$page.params.bot_id as string]
			},
			{
				axios: {
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			}
		)
	);

	let isBulkDeleting = $state(false);
	let creatingParams = $state(false);
	let newParams: ParamRequest[] = $state([
		{
			key: '',
			value: ''
		}
	]);
	let bulkDeleteKeys: string[] = $state([]);
	function increaseBulkDeleteKeys(key: string) {
		if (bulkDeleteKeys.find((k) => k === key)) {
			bulkDeleteKeys = bulkDeleteKeys.filter((k) => k !== key);
			return;
		}
		bulkDeleteKeys = [...bulkDeleteKeys, key];
	}

	let isIncludedOnKeys = $derived((Key: string) => {
		return bulkDeleteKeys.find((k) => k === Key);
	});

	async function confirmBulkDeleteParams() {
		try {
			if (!bulkDeleteKeys.length) {
				toast('No params selected', 'error');
				return;
			}

			await postParamsBulkDelete(
				{
					keys: bulkDeleteKeys,
					scope: 'bot',
					scopeId: $page.params.bot_id as string
				},
				{
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			);
			toast('Params deleted', 'success');
			isBulkDeleting = false;
			bulkDeleteKeys = [];
			$params.refetch();
		} catch (error) {
			handleErr(error);
		}
	}

	function increaseParams() {
		newParams = [
			...newParams,
			{
				key: '',
				value: ''
			}
		];
	}

	function decreaseParams() {
		newParams = newParams.slice(0, newParams.length - 1);
	}

	async function confirmCreateParams() {
		try {
			// await postParams(
			// 	{
			// 		params: newParams,
			// 		scope: 'bot',
			// 		scopeId: $page.params.bot_id as string
			// 	},
			// 	{
			// 		headers: {
			// 			Authorization: `Bearer ${getStorage('token')}`
			// 		}
			// 	}
			// );

			toast('Params created', 'success');
			creatingParams = false;
			$params.refetch();
			newParams = [
				{
					key: '',
					value: ''
				}
			];
		} catch (error) {
			handleErr(error);
		}
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Bot Params';
	});
</script>

<svelte:head>
	<title>Bot parameters | Atomic Bot Manager</title>
</svelte:head>

<section class="h-full">
	{#if !$params?.data?.data.data.length}
		<div class="flex h-full flex-col items-center justify-center gap-2">
			<div class="text-center">
				<h1 class="text-2xl font-semibold">Params not found</h1>
				<p>You can manage your bot params here.</p>
			</div>
			<Button on:click={() => (creatingParams = true)}>Create first param</Button>
		</div>
	{:else}
		<h1 class="font-semibold">Params list</h1>
		<p class="text-xs">Manage the bot params here</p>
		{#if isBulkDeleting}
			<h2 class="mt-3 text-xs font-semibold">Select the params you want to delete</h2>
		{/if}
		<div class="mt-3">
			{#if $params.isLoading}
				<div class="flex h-full items-center justify-center">
					<BaseLoading title="Loading params..." />
				</div>
			{:else if $params.isError}
				<div class="flex h-full flex-col items-center justify-center">
					<h1 class="text-2xl font-semibold">Failed to load params</h1>
					{$params.failureReason?.message}
					<Button class="mt-2" on:click={() => $params.refetch()}>Retry</Button>
				</div>
			{:else if $params?.data?.data.data}
				<div class="flex gap-2">
					{#each $params?.data?.data.data as param, i (i)}
						<div animate:flip={{ duration: 200 }}>
							<Card.Main
								freeSize
								class="max-w-fit {!isBulkDeleting &&
									'!pointer-events-none !cursor-auto'} {isIncludedOnKeys(param.key) &&
									'ring-1 ring-white'}"
								asButton
								on:click={() => increaseBulkDeleteKeys(param.key)}
							>
								<Card.Header hideIcon={true} titleClass="text-xs" title={param.key} />
								<Card.Body class="mt-0 flex items-center justify-center px-3 pb-2 pt-1 font-bold">
									{param.value}
								</Card.Body>
							</Card.Main>
						</div>
					{/each}
				</div>
			{:else}
				<div class="flex h-full flex-col items-center justify-center">
					<h1 class="text-2xl font-semibold">No params found</h1>
				</div>
			{/if}
		</div>
		<div class="mt-2 flex gap-2">
			{#if !isBulkDeleting}
				<Button on:click={() => (isBulkDeleting = true)}>Delete params</Button>
				<Button on:click={() => (creatingParams = true)}>Create param</Button>
			{:else}
				<Button
					on:click={async () => {
						isBulkDeleting = false;
						bulkDeleteKeys = [];
					}}>Cancel</Button
				>
				<Button on:click={confirmBulkDeleteParams}>Confirm</Button>
			{/if}
		</div>
	{/if}
</section>

<BaseModal
	open={creatingParams}
	on:openChange={(e) => (creatingParams = e.detail)}
	title="New bot param"
	contentClass="max-w-[460px]"
	transitionDuration={0}
>
	<form onsubmit={preventDefault(confirmCreateParams)}>
		<div class="mt-2 max-h-[500px] overflow-y-auto pr-2">
			{#each newParams as param, i}
				<Input
					id={`key-${i.toString()}`}
					required
					label="Key"
					description="The key of the param"
					placeholder="Insert a key"
					wrapperClass={`${i === 0 ? 'mt-2' : 'mt-3'}`}
					bind:value={param.key}
				/>
				<Input
					id={`value-${i.toString()}`}
					required
					label="Value"
					description="The value of the param"
					placeholder="Insert a value"
					wrapperClass="mt-2"
					bind:value={param.value}
				/>
				<div class="flex justify-between">
					{#if i === newParams.length - 1}
						<Button icon="ic:baseline-plus" small class="mt-2" on:click={increaseParams}
							>Increase</Button
						>
					{/if}
					{#if newParams.length > 1}
						<Button small icon="ic:round-remove" class="mt-2" on:click={decreaseParams}></Button>
					{/if}
				</div>
			{/each}
		</div>
		<div class="mt-5 flex justify-end gap-2">
			<Button on:click={() => (creatingParams = false)}>Cancel</Button>
			<Button type="submit">Create</Button>
		</div>
	</form>
</BaseModal>
