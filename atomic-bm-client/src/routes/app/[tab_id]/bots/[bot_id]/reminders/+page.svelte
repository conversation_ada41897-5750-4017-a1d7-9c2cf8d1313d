<script lang="ts">
	import Select from '$lib/components/ui/select.svelte';
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { reminderStore } from '$stores/reminder-store';
	import { addMinutesToCurrentDate } from '$utils';
	import Button from '$lib/components/ui/button.svelte';
	import Input from '$lib/components/ui/input.svelte';
	import * as Card from '$lib/components/ui/card';

	let futureDate: {
		label: '';
		value: '';
	} = $state();
	let message = $state('');

	let expiryDate = $derived(reminderStore.getExpiryDate());
	let expiryMessage = $derived(reminderStore.getExpiryMessage());

	const minuteOptions = [
		{
			label: '1 minute',
			value: '1'
		},
		{
			label: '5 minutes',
			value: '5'
		},
		{
			label: '15 minutes',
			value: '15'
		},
		{
			label: '30 minutes',
			value: '30'
		},
		{
			label: '45 minutes',
			value: '45'
		},
		{
			label: '1 hour',
			value: '60'
		},
		{
			label: '2 hours',
			value: '120'
		}
	];

	function stop(): void {
		reminderStore.stopTimer();
		expiryDate = reminderStore.getExpiryDate();
		expiryMessage = reminderStore.getExpiryMessage();
	}

	function createReminder(): void {
		const newDate = addMinutesToCurrentDate(futureDate.value);
		reminderStore.startTimer(newDate, message);
		expiryDate = reminderStore.getExpiryDate();
		expiryMessage = reminderStore.getExpiryMessage();
		message = '';
	}

	function refresh(): void {
		expiryDate = reminderStore.getExpiryDate();
		expiryMessage = reminderStore.getExpiryMessage();
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Bot Reminders';
	});
</script>

<svelte:head>
	<title>Bot reminders | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[420px]">
		<Card.Header title="Bot reminder" titleClass="text-base" class="h-auto py-1" hideIcon />
		<Card.Body class="pt-1">
			{#if !expiryDate}
				<p class="text-sm">You can set a reminder for an important note or event in the future.</p>
				<div class="mt-3 flex flex-col gap-2">
					<Input label="Message (optional)" placeholder="Enter message" bind:value={message} />
					<Select
						options={minuteOptions}
						label="Minutes"
						placeholder="Select the amount of minutes"
						on:selected={(e) => (futureDate = e.detail)}
					/>
					<Button class="mt-3" full on:click={createReminder}>Create reminder</Button>
				</div>
			{:else}
				<p>This reminder will fire at <strong>{expiryDate}</strong></p>
				{#if expiryMessage}
					<p class="my-2 bg-gray-900 p-2 text-sm tracking-wide">{expiryMessage}</p>
				{/if}
				<div class="mt-3 space-y-2">
					<Button full on:click={stop}>Stop reminder</Button>
					<Button full on:click={refresh}>Refresh</Button>
				</div>
			{/if}
		</Card.Body>
	</Card.Main>
</section>
