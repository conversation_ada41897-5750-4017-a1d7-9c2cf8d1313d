<script lang="ts">
	import type { GetBotsParams } from '$client/api.schemas';
	import { createGetBots, createGetBotsState, createDeleteBotsBotId } from '$client/bot';
	import BaseFilters from '$lib/components/base-filters.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import { appStore } from '$stores/app-store';
	import { handleErr, persistData, tabNavigate, toast } from '$utils';
	import { onMount } from 'svelte';
	import BotOverview from '$lib/components/bot/bot-overview/bot-overview.svelte';
	import Input from '$lib/components/ui/input.svelte';

	let deletingBot = $state(false);
	let selectedBotID = $state('');
	let filters: GetBotsParams = $state({
		accountId: undefined,
		symbol: undefined,
		region: undefined,
		status: undefined,
		isDeleted: undefined,
		orderBy: undefined,
		orderDirection: undefined,
		limit: undefined,
		offset: undefined
	});

	const botsQuery = $derived(
		createGetBots(filters, {
			query: {
				retry: false
			}
		})
	);

	const botsStateQuery = $derived(
		createGetBotsState({
			query: {
				retry: false
			}
		})
	);

	const deleteBotMutation = $derived(
		createDeleteBotsBotId({
			mutation: {
				onSuccess: () => {
					$botsQuery.refetch();
					toast('Bot deleted successfully', 'success');
					deletingBot = false;
					selectedBotID = '';
				},
				onError: (error) => {
					handleErr(error);
					deletingBot = false;
				}
			}
		})
	);

	const botList = $derived($botsQuery.data?.data);
	const botStateList = $derived($botsStateQuery.data?.data);

	$effect(() => {
		console.log(botStateList);
		console.log(botList);
	});

	async function confirmDeleteBot() {
		if (!selectedBotID) return;

		try {
			deletingBot = true;
			await $deleteBotMutation.mutateAsync({ botId: selectedBotID });
		} catch (error) {
			console.error('Delete failed:', error);
		}
	}

	function openDeleteModal(botId: string) {
		selectedBotID = botId;
		deletingBot = true;
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Bots Overview';
	});
</script>

<svelte:head>
	<title>Bots Overview | Atomic Bot Manager</title>
</svelte:head>

<section>
	<div class="flex justify-between p-1.5">
		<div class="flex flex-1 flex-wrap items-end gap-2">
			<div>
				<Input id="bot-search" placeholder="Search for by region, symbol, accountId, status, tag" />
			</div>
			<BaseFilters
				pageLimit={filters.limit}
				directionOption={filters.orderDirection}
				botsOrderBy={filters.orderBy}
				activeState={filters.isDeleted}
				accountId={filters.accountId}
				symbol={filters.symbol}
				status={filters.status}
				on:pageLimit={async (e) => {
					await persistData(undefined, 'limitbots', true, e.detail);
					filters.limit = e.detail;
				}}
				on:direction={async (e) => {
					await persistData(undefined, 'directionbots', true, e.detail);
					filters.orderDirection = e.detail;
				}}
				on:botsOrderBy={async (e) => {
					await persistData(undefined, 'orderbybots', true, e.detail);
					filters.orderBy = e.detail;
				}}
				on:activeState={async (e) => {
					await persistData(undefined, 'isdeletedbots', true, e.detail);
					filters.isDeleted = e.detail;
				}}
				on:filterOptions={async (e) => {
					e.detail.accountId &&
						(await persistData(undefined, 'accountidbots', true, e.detail.accountId));
					e.detail.symbol && (await persistData(undefined, 'symbolbots', true, e.detail.symbol));
					e.detail.status && (await persistData(undefined, 'statusbots', true, e.detail.status));
					filters.accountId = e.detail.accountId;
					filters.symbol = e.detail.symbol;
					filters.status = e.detail.status;
				}}
				on:refresh={() => $botsQuery.refetch()}
			/>
		</div>
		<div>
			<Button on:click={() => tabNavigate('New bot', '/bots/create')}>New bot</Button>
		</div>
	</div>

	<BotOverview />
</section>
