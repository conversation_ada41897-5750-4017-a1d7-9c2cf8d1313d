<script lang="ts">
	import { page } from '$app/state';
	import { appStore } from '$stores/app-store';
	import * as Card from '$lib/components/ui/card';
	import Input from '$lib/components/ui/input.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import Icon from '$lib/components/ui/icon.svelte';
	import { onMount } from 'svelte';
	import {
		tabNavigate,
		getPersistData,
		persistData,
		clearPersistData,
		handleErr,
		toast
	} from '$utils';
	import { browser } from '$app/environment';
	import { postBots } from '$client/bot';
	import { ssp, queryParameters } from 'sveltekit-search-params';
	import { z } from 'zod';

	const queryStore = queryParameters({
		accInfo: ssp.array()
	});

	let { data } = $props();

	let account = $state({
		label: '',
		value: ''
	});
	let base = $state('');
	let quote = $state('');
	let tag = $state('');

	let open = $state(false);
	let isSubmitting = $state(false);
	let activeInput = $state<'base' | 'quote' | 'tag'>('base');
	let formErrors = $state<Record<string, string>>({});

	const symbol = $derived(`${base}-${quote}`);

	// Zod validation schemas
	const baseSchema = z
		.string()
		.min(1, 'Base currency is required')
		.max(4, 'Base currency must be 4 characters or less')
		.regex(/^[A-Z0-9]+$/, 'Base currency must contain only uppercase letters and numbers');

	const quoteSchema = z
		.string()
		.min(1, 'Quote currency is required')
		.max(6, 'Quote currency must be 6 characters or less')
		.regex(/^[A-Z0-9]+$/, 'Quote currency must contain only uppercase letters and numbers');

	const tradingPairSchema = z
		.object({
			base: baseSchema,
			quote: quoteSchema
		})
		.refine((data) => data.base !== data.quote, {
			message: 'Base and quote currencies cannot be the same',
			path: ['quote']
		});

	const formSchema = z.object({
		account: z.object({
			label: z.string().min(1, 'Account is required'),
			value: z.string().min(1, 'Account is required')
		}),
		tradingPair: tradingPairSchema,
		tag: z.string().min(1, 'Tag is required').max(50, 'Tag must be 50 characters or less')
	});

	$effect(() => {
		if (browser && page) {
			const accountFromPersist = getPersistData('newbotaccount');
			if (accountFromPersist) {
				account = accountFromPersist;
			} else {
				account = { label: '', value: '' };
			}

			const existingSymbol = getPersistData('newbotsymbol') || '';
			if (existingSymbol.includes('-')) {
				const [existingBase, existingQuote] = existingSymbol.split('-');
				base = existingBase || '';
				quote = existingQuote || '';
			} else {
				base = existingSymbol || '';
				quote = '';
			}

			tag = getPersistData('newbottag') || '';
		}
	});

	function clearFieldsData() {
		clearPersistData('newbotaccount');
		clearPersistData('newbotsymbol');
		clearPersistData('newbottag');
	}

	function returnToBots() {
		clearFieldsData();
		tabNavigate('Bots', '/bots');
	}

	let accountOptions = $derived(
		data.accounts?.data.map((acc: any) => {
			if ($queryStore.accInfo) {
				if ($queryStore.accInfo[1] === acc.exchangeId) {
					account = { label: acc.exchangeId, value: acc.id };
				}
			}
			return { label: acc.exchangeId, value: acc.id };
		})
	);

	const selectedAccountLabel = $derived(
		accountOptions?.find((acc: any) => acc.value === account.value)?.label || ''
	);

	function closeCombobox() {
		open = false;
	}

	async function selectAccount(selectedAccount: { label: string; value: string }) {
		if (formErrors.account) {
			const { account, ...rest } = formErrors;
			formErrors = rest;
		}

		await persistData(undefined, 'newbotaccount', true, selectedAccount);
		account = selectedAccount;
		closeCombobox();
	}

	async function handleBaseInput(e: Event) {
		if (formErrors.base) {
			const { base, ...rest } = formErrors;
			formErrors = rest;
		}

		const target = e.target as HTMLInputElement;
		let value = target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');

		if (value.length > 4) {
			value = value.slice(0, 4);
		}

		base = value;
		await persistData(undefined, 'newbotsymbol', true, symbol);

		if (value.length === 4) {
			activeInput = 'quote';
			setTimeout(() => {
				const quoteInput = document.getElementById('quote-input') as HTMLInputElement;
				if (quoteInput) quoteInput.focus();
			}, 0);
		}
	}

	async function handleQuoteInput(e: Event) {
		if (formErrors.quote) {
			const { quote, ...rest } = formErrors;
			formErrors = rest;
		}

		const target = e.target as HTMLInputElement;
		let value = target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');

		if (value.length > 6) {
			value = value.slice(0, 6);
		}

		if (value.length === 6) {
			activeInput = 'tag';
			setTimeout(() => {
				const tagInput = document.getElementById('tag') as HTMLInputElement;
				if (tagInput) tagInput.focus();
			}, 0);
		}

		quote = value;
		await persistData(undefined, 'newbotsymbol', true, symbol);

		if (value.length === 0) {
			activeInput = 'base';
			setTimeout(() => {
				const baseInput = document.getElementById('base-input') as HTMLInputElement;
				if (baseInput) baseInput.focus();
			}, 0);
		}
	}

	function handleKeyNavigation(e: KeyboardEvent, currentInput: 'base' | 'quote') {
		if (e.key === 'Enter' || e.key === 'Tab') {
			e.preventDefault();
			if (currentInput === 'base') {
				activeInput = 'quote';
				setTimeout(() => {
					const quoteInput = document.getElementById('quote-input') as HTMLInputElement;
					if (quoteInput) quoteInput.focus();
				}, 0);
			} else {
				activeInput = 'base';
				setTimeout(() => {
					const tagInput = document.getElementById('tag') as HTMLInputElement;
					if (tagInput) tagInput.focus();
				}, 0);
			}
		}
	}

	function handleInputFocus(inputType: 'base' | 'quote') {
		activeInput = inputType;
	}

	function validateForm(): { isValid: boolean; errors: Record<string, string> } {
		const formData = {
			account: {
				label: account.label,
				value: account.value
			},
			tradingPair: {
				base: base.trim(),
				quote: quote.trim()
			},
			tag: tag.trim()
		};

		try {
			formSchema.parse(formData);
			return { isValid: true, errors: {} };
		} catch (error) {
			if (error instanceof z.ZodError) {
				const errors: Record<string, string> = {};
				const zodError = error as z.ZodError;

				zodError.issues.forEach((issue) => {
					const path = issue.path.join('.');
					if (path === 'account.label' || path === 'account.value') {
						errors.account = issue.message;
					} else if (path === 'tradingPair.base') {
						errors.base = issue.message;
					} else if (path === 'tradingPair.quote') {
						errors.quote = issue.message;
					} else if (path === 'tag') {
						errors.tag = issue.message;
					}
				});

				return { isValid: false, errors };
			}
			return { isValid: false, errors: { general: 'Validation failed' } };
		}
	}

	async function confirmCreateBot() {
		formErrors = {};

		const validation = validateForm();

		if (!validation.isValid) {
			formErrors = validation.errors;
			return;
		}

		try {
			isSubmitting = true;
			await postBots({
				accountId: account.value,
				symbol: symbol.trim(),
				tag: tag.trim()
			});

			clearFieldsData();
			toast('Bot created successfully', 'success');
			tabNavigate('Bots', '/bots');
		} catch (error) {
			handleErr(error);
		} finally {
			isSubmitting = false;
		}
	}

	function checkNewAccountFromRoute() {
		const accLabel = page.url.searchParams.get('accLabel');
		const accValue = page.url.searchParams.get('accValue');
		if (accLabel && accValue) {
			account.label = accLabel;
			account.value = accValue;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'New bot';
		checkNewAccountFromRoute();
	});
</script>

<svelte:head>
	<title>New bot | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={confirmCreateBot}>
		<Card.Header title="New bot" on:iconClick={returnToBots} />
		<Card.Body class="space-y-4">
			<div class="flex w-full flex-col gap-1.5">
				<div class="flex flex-col">
					<span class="text-xs opacity-90">
						Account
						<span class="text-xs text-red-900">*</span>
					</span>
					<span class="text-[0.64rem] opacity-70">Every bot must be linked to an account</span>
				</div>

				<Popover.Root bind:open>
					<Popover.Trigger class="w-full">
						<Button
							class="!w-full justify-between border-gray-800 bg-dark-7 text-xs text-white hover:border-gray-700 focus:border-gray-600"
							role="combobox"
							aria-expanded={open}
							disabled={isSubmitting}
						>
							<span class="truncate">
								{selectedAccountLabel || 'Select an account'}
							</span>
							<Icon
								icon="ph:caret-down"
								class="ml-2 opacity-50 transition-transform {open ? 'rotate-180' : ''}"
							/>
						</Button>
					</Popover.Trigger>
					<Popover.Content class="w-[360px] border border-gray-800 bg-dark-9 p-0 shadow-lg">
						<Command.Root class="border-0 bg-dark-9 text-white">
							<Command.Input
								placeholder="Search accounts..."
								class="border-b border-gray-800 bg-dark-9 text-xs text-white placeholder:text-gray-500"
							/>
							<Command.List class="max-h-[200px] bg-dark-9">
								<Command.Empty class="py-4 text-xs text-gray-200">No account found.</Command.Empty>
								<Command.Group>
									{#each accountOptions || [] as accountOption (accountOption.value)}
										<Command.Item
											value={accountOption.value}
											onSelect={() => selectAccount(accountOption)}
											class="text-whitre flex cursor-pointer items-center gap-2 bg-dark-8 px-3 py-2 text-xs text-white hover:bg-dark-6 aria-selected:bg-dark-6 aria-selected:text-white"
										>
											<span class="truncate capitalize">{accountOption.label}</span>
										</Command.Item>
									{/each}
								</Command.Group>
								<div class="border-t border-gray-800 p-2">
									<Button
										small
										on:click={() => {
											closeCombobox();
											tabNavigate('New account', '/accounts/create?returnToBots=true');
										}}
									>
										<Icon icon="material-symbols:add" class="mr-1" />
										Create new account
									</Button>
								</div>
							</Command.List>
						</Command.Root>
					</Popover.Content>
				</Popover.Root>

				{#if formErrors.account}
					<div class="ml-1 mt-1 text-xs text-red-500">
						{formErrors.account}
					</div>
				{/if}
			</div>

			<!-- Trading Pair Inputs -->
			<div class="flex w-full flex-col gap-1.5">
				<div class="flex flex-col">
					<span class="text-xs opacity-90">
						Trading Pair
						<span class="text-xs text-red-900">*</span>
					</span>
					<span class="text-[0.64rem] opacity-70">
						Enter base and quote currencies. Use Tab or Enter to navigate between fields.
					</span>
				</div>

				<div class="flex gap-2">
					<!-- Base Input -->
					<div class="flex-1">
						<Input
							id="base-input"
							required
							label=""
							placeholder="BTC"
							maxlength={4}
							on:input={handleBaseInput}
							on:keydown={(e) => handleKeyNavigation(e, 'base')}
							on:focus={() => handleInputFocus('base')}
							value={base}
							disabled={isSubmitting}
							class="text-center text-sm font-medium placeholder:opacity-20"
						/>
					</div>

					<!-- Separator -->
					<div class="flex items-center justify-center px-2">
						<span class="text-lg font-bold text-gray-400">-</span>
					</div>

					<!-- Quote Input -->
					<div class="flex-1">
						<Input
							id="quote-input"
							required
							maxlength={6}
							label=""
							placeholder="USDT"
							on:input={handleQuoteInput}
							on:keydown={(e) => handleKeyNavigation(e, 'quote')}
							on:focus={() => handleInputFocus('quote')}
							value={quote}
							disabled={isSubmitting}
							class="text-center text-sm font-medium placeholder:opacity-20"
						/>
					</div>
				</div>

				<!-- Visual indicator for active input -->
				<div class="mt-1 flex justify-center gap-2">
					<div
						class="h-1 w-8 rounded-full transition-colors {activeInput === 'base'
							? 'bg-blue-500'
							: 'bg-gray-600'}"
					></div>
					<div
						class="h-1 w-8 rounded-full transition-colors {activeInput === 'quote'
							? 'bg-blue-500'
							: 'bg-gray-600'}"
					></div>
				</div>

				<!-- Trading pair errors -->
				{#if formErrors.base || formErrors.quote}
					<div class="ml-1 mt-1 text-xs text-red-500">
						{#if formErrors.base}
							{formErrors.base}
						{:else if formErrors.quote}
							{formErrors.quote}
						{/if}
					</div>
				{/if}
			</div>

			<Input
				id="tag"
				required
				label="Tag"
				description="The tag of the bot"
				placeholder="Insert a Tag"
				on:input={async (e) => {
					if (formErrors.tag) {
						const { tag, ...rest } = formErrors;
						formErrors = rest;
					}

					tag = await persistData(e, 'newbottag');
				}}
				value={tag}
				disabled={isSubmitting}
			/>

			{#if formErrors.tag}
				<div class="ml-1 mt-1 text-xs text-red-500">
					{formErrors.tag}
				</div>
			{/if}
		</Card.Body>
		<Card.Footer
			on:cancel={returnToBots}
			confirmBtnType="submit"
			confirmText={isSubmitting ? 'Creating...' : 'Create Bot'}
		/>
	</Card.Main>
</section>
