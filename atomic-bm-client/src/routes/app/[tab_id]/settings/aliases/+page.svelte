<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import ConfirmModal from '$lib/components/confirm-modal.svelte';
	import BaseModal from '$lib/components/base-modal.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import Input from '$lib/components/ui/input.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
	import { profile } from '$stores/profile-store';
	import { aliasClient } from '$api/aliases';
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { toast } from '$utils';

	let newAlias = $state('');
	let command = $state('');
	let creatingAlias = $state(false);
	let deletingAlias = $state(false);
	let selectedAliasID = $state('');
	let aliases: any = $state([]);

	async function confirmCreateAlias() {
		try {
			const res = await aliasClient($profile.id).createAlias(
				{
					id: window.crypto.randomUUID(),
					alias: newAlias,
					command,
					createdAt: new Date().toISOString()
				},
				$profile.id
			);

			if (res !== null) {
				throw new Error(res.message);
			}

			newAlias = '';
			command = '';
			creatingAlias = false;
			toast('Alias created', 'success');
			loadAliases();
		} catch (err) {
			console.error(err);
		}
	}

	async function confirmDeleteAlias() {
		try {
			const res = await aliasClient($profile.id).removeAlias(selectedAliasID);
			if (res !== null) {
				throw new Error(res.message);
			}

			toast('Alias deleted', 'success');
			loadAliases();
			deletingAlias = false;
		} catch (err) {
			console.error(err);
		}
	}

	async function loadAliases() {
		try {
			const { aliases: data, err } = await aliasClient($profile.id).fetchAliases();
			if (err) {
				throw new Error(err.message);
			}

			aliases = data;
		} catch (err) {
			console.error(err);
		}
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Aliases';
		loadAliases();
	});
</script>

<svelte:head>
	<title>Aliases | Atomic Bot Manager</title>
</svelte:head>

<section>
	<h1 class="text-base font-bold">Terminal aliases</h1>
	<p class="text-sm">You can manage your terminal command aliases here.</p>
	<div class="flex flex-wrap gap-2 mt-4">
		{#each aliases as alias}
			<div class="border border-gray-800">
				<div class="flex items-center justify-between gap-2 p-2 text-sm border-b border-gray-800">
					<h1>{alias.alias}</h1>
					<Icon
						btn
						icon="mdi:trash-outline"
						on:click={() => {
							deletingAlias = true;
							selectedAliasID = alias.id;
						}}
					/>
				</div>
				<div class="p-2 text-xs">
					<h2 class="text-gray-400">{alias.command}</h2>
				</div>
			</div>
		{:else}
			<div class="w-full">No aliases found</div>
		{/each}
	</div>
	<div class="mt-3">
		<Button on:click={() => (creatingAlias = true)}>New alias</Button>
	</div>
</section>

<BaseModal open={creatingAlias} on:openChange={(e) => (creatingAlias = e.detail)} title="New alias">
	<form class="flex flex-col gap-3" onsubmit={preventDefault(confirmCreateAlias)}>
		<Input
			label="Alias name"
			description="The name of the alias"
			placeholder="ex: new bot"
			required
			bind:value={newAlias}
		/>
		<Input
			label="Command"
			description="The command of the alias"
			placeholder="ex: create new bot"
			required
			bind:value={command}
		/>
		<div class="flex justify-end gap-2 mt-3">
			<Button on:click={() => (creatingAlias = false)}>Cancel</Button>
			<Button type="submit">Create</Button>
		</div>
	</form>
</BaseModal>

<ConfirmModal
	title="Delete alias"
	description="Are you sure you want to delete this alias?"
	open={deletingAlias}
	on:openChange={(e) => (deletingAlias = e.detail)}
	on:cancel={() => (deletingAlias = false)}
	on:confirm={confirmDeleteAlias}
/>
