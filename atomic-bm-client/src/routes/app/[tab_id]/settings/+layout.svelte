<script lang="ts">
	import { settingsOptions, appStore } from '$stores/app-store';
	import { tabNavigate } from '$utils';
	import Button from '$lib/components/ui/button.svelte';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();
</script>

<main class="flex h-full w-full flex-col">
	<div class="flex border-b border-b-gray-900">
		{#each $settingsOptions as setting}
			<Button
				class="w-[120px] rounded-none border-b-0 border-l-0 border-r-0 border-t-0 bg-inherit text-gray-500 {$appStore.currentPageTitle ===
					setting.tabName && 'border-b !border-b-gray-300 text-gray-100'}"
				icon={setting.icon}
				iconClass="text-gray-500 {$appStore.currentPageTitle === setting.tabName
					? 'text-gray-100'
					: ''}"
				on:click={() => tabNavigate(setting.tabName, setting.path)}
			>
				{setting.name}
			</Button>
		{/each}
	</div>
	<div class="flex-1 px-4 py-2">
		{@render children?.()}
	</div>
</main>
