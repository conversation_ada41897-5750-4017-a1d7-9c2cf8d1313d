import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';

interface RatioVisualizationProps {
    buyPercentage: number;
    sellPercentage: number;
}

export const RatioVisualization: React.FC<RatioVisualizationProps> = ({
                                                                          buyPercentage,
                                                                          sellPercentage,
                                                                      }) => {
    const styles = useStyles2(getStyles);

    return (
        <div className={styles.ratioContainer}>
            <div className={styles.ratioBarWrapper}>
                <div className={styles.ratioBar}>
                    <div
                        className={styles.buyRatio}
                        style={{ width: `${buyPercentage}%` }}
                    />
                    <div
                        className={styles.ratioDivider}
                        style={{ left: `${buyPercentage}%` }}
                    />
                </div>
            </div>
            <div className={styles.percentageContainer}>
                <div className={styles.buyPercentage}>
                    B {buyPercentage.toFixed(2)}%
                </div>
                <div className={styles.sellPercentage}>
                    {sellPercentage.toFixed(2)}% S
                </div>
            </div>
        </div>
    );
};

const getStyles = () => ({
    ratioContainer: css`
    padding: 0 12px;
    background-color: #121722;
    height: 40px;
    position: relative;
  `,
    percentageContainer: css`
    display: flex;
    justify-content: space-between;
    padding-top: 3px;
    position: relative;
    z-index: 3;
  `,
    buyPercentage: css`
    color: #00b164;
    font-size: 12px;
    font-weight: 500;
  `,
    sellPercentage: css`
    color: #ea0070;
    font-size: 12px;
    font-weight: 500;
  `,
    ratioBarWrapper: css`
    position: absolute;
    left: 70px;
    right: 70px;
    top: 10px;
    height: 6px;
    z-index: 1;
  `,
    ratioBar: css`
    width: 100%;
    height: 100%;
    background-color: #ea0070;
    position: relative;
    border-radius: 3px;
  `,
    buyRatio: css`
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #00b164;
    border-radius: 3px 0 0 3px;
  `,
    ratioDivider: css`
    position: absolute;
    top: -2px;
    bottom: -2px;
    width: 2px;
    background-color: #0b0f19;
    z-index: 2;
  `,
});
