import React, { useRef, useEffect, useState } from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderbookSummary, PriceDirection } from '../../types';
import { formatPrice, formatTotal } from '../../utils/formatters';
import { UpArrowIcon, DownArrowIcon } from '../icons/ArrowIcons';

interface SummaryBarProps {
    summary: OrderbookSummary;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    tradingData?: any;
    isMobile?: boolean;
}

export const SummaryBar: React.FC<SummaryBarProps> = ({
                                                          summary,
                                                          decimalPlaces,
                                                          tradingData,
                                                          isMobile = false,
                                                      }) => {
    const styles = useStyles2(getStyles);
    const containerRef = useRef<HTMLDivElement>(null);
    const [containerWidth, setContainerWidth] = useState<number>(0);

    // Monitor container width using ResizeObserver
    useEffect(() => {
        if (!containerRef.current) return;

        const resizeObserver = new ResizeObserver(entries => {
            if (entries.length > 0) {
                setContainerWidth(entries[0].contentRect.width);
            }
        });

        resizeObserver.observe(containerRef.current);
        return () => resizeObserver.disconnect();
    }, []);

    // Get price direction from summary or default to neutral
    const priceDirection: PriceDirection = summary.priceDirection || 'neutral';

    // Get CSS class based on price direction
    const getPriceClasses = () => {
        if (priceDirection === 'up') {
            return styles.priceUp;
        } else if (priceDirection === 'down') {
            return styles.priceDown;
        }
        return '';
    };

    // Determine layout based on container width
    const isNarrow = containerWidth < 600;

    return (
        <div ref={containerRef} className={styles.summaryBar}>
            <div className={styles.summaryItem}>
                <div className={styles.summaryLabel}>Total Bid Volume</div>
                <div className={cx(styles.summaryValue, styles.buyValue)}>
                    {formatTotal(summary.buyVolume, decimalPlaces.total)}
                </div>
            </div>

            <div className={styles.summaryItem}>
                <div className={styles.summaryLabel}>Mid Price</div>
                <div className={cx(styles.summaryValue, getPriceClasses())}>
                    {formatPrice(summary.avgPrice, decimalPlaces.price)}
                    <span className={styles.priceIconContainer}>
                        {priceDirection === 'up' && <UpArrowIcon className={styles.priceIcon} />}
                        {priceDirection === 'down' && <DownArrowIcon className={styles.priceIcon} />}
                        {priceDirection === 'neutral' && <span className={styles.invisibleIcon} />}
                    </span>
                </div>
            </div>

            {/* Only show spread on mobile */}
            {isMobile ? (
                <div className={styles.summaryItem}>
                    <div className={styles.summaryLabel}>Spread</div>
                    <div className={styles.summaryValue}>
                        <span>{summary.spreadPercentage.toFixed(4)}%</span>
                    </div>
                </div>
            ) : (
                <>
                    <div className={styles.summaryItem}>
                        <div className={styles.summaryLabel}>Top Spread</div>
                        <div className={styles.summaryValue}>
                            <span>{summary.spreadPercentage.toFixed(4)}%</span>
                        </div>
                    </div>

                    {!isNarrow && (
                        <div className={styles.summaryItem}>
                            <div className={styles.summaryLabel}>Imbalance</div>
                            <div className={styles.summaryValue}>
                                {(summary.buyPercentage / summary.sellPercentage).toFixed(2)}
                            </div>
                        </div>
                    )}
                </>
            )}

            <div className={styles.summaryItem}>
                <div className={styles.summaryLabel}>Total Ask Volume</div>
                <div className={cx(styles.summaryValue, styles.sellValue)}>
                    {formatTotal(summary.sellVolume, decimalPlaces.total)}
                </div>
            </div>
        </div>
    );
};

const getStyles = () => ({
    summaryBar: css`
    display: flex;
    padding: 8px 12px;
    justify-content: space-between;
    background-color: #121722;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
    min-height: 60px;
  `,
    summaryItem: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 4px 8px;
    min-width: 80px;
  `,
    summaryLabel: css`
    font-size: 11px;
    color: #9096a1;
    margin-bottom: 2px;
    white-space: nowrap;
    text-align: center;
  `,
    summaryValue: css`
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    white-space: nowrap;
  `,
    buyValue: css`
    color: #00b164;
  `,
    sellValue: css`
    color: #ea0070;
  `,
    refValue: css`
    color: #5794F2;
  `,
    priceUp: css`
    color: #00b164;
    transition: color 0.3s ease;
    animation: pulse-green 0.6s ease-out;
    
    @keyframes pulse-green {
      0% { opacity: 1; }
      50% { opacity: 0.7; transform: scale(1); }
      100% { opacity: 1; transform: scale(1); }
    }
  `,
    priceDown: css`
    color: #ea0070;
    transition: color 0.3s ease;
    animation: pulse-red 0.6s ease-out;
    
    @keyframes pulse-red {
      0% { opacity: 1; }
      50% { opacity: 0.7; transform: scale(1); }
      100% { opacity: 1; transform: scale(1); }
    }
  `,
    priceIconContainer: css`
    display: inline-flex;
    align-items: center;
    margin-left: 2px;
    height: 24px;
  `,
    priceIcon: css`
    width: 14px;
    height: 14px;
  `,
    invisibleIcon: css`
    display: inline-block;
    width: 14px;
    height: 14px;
  `,
});
