import React, { useEffect, useState } from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';

interface LiveIndicatorProps {
    isLive: boolean;
}

export const LiveIndicator: React.FC<LiveIndicatorProps> = ({ isLive }) => {
    const styles = useStyles2(getStyles);
    const [animateChange, setAnimateChange] = useState(false);

    // Add animation effect when status changes
    useEffect(() => {
        setAnimateChange(true);
        const timer = setTimeout(() => setAnimateChange(false), 500);
        return () => clearTimeout(timer);
    }, [isLive]);

    return (
        <div className={styles.container}>
            <div
                className={cx(
                    styles.indicator,
                    isLive ? styles.liveIndicator : styles.historyIndicator,
                    animateChange && styles.statusChanged
                )}
            />
            <span className={cx(
                styles.label,
                animateChange && styles.labelChanged
            )}>
                {isLive ? 'Live' : 'Historical'}
            </span>
        </div>
    );
};

const getStyles = () => ({
    container: css`
        display: flex;
        align-items: center;
        gap: 6px;
    `,
    indicator: css`
        width: 10px;
        height: 10px;
        border-radius: 50%;
        transition: background-color 0.3s ease, box-shadow 0.3s ease;
    `,
    liveIndicator: css`
        background-color: #00b164;
        box-shadow: 0 0 5px rgba(0, 177, 100, 0.7);
        animation-name: pulse-green;
        animation-duration: 1s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
        
        @keyframes pulse-green {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }
    `,
    historyIndicator: css`
        background-color: #ea0070;
        box-shadow: 0 0 5px rgba(234, 0, 112, 0.7);
    `,
    statusChanged: css`
        transform: scale(1.5);
        transition: transform 0.3s ease;
    `,
    label: css`
        font-size: 12px;
        font-weight: 500;
        transition: color 0.3s ease;
    `,
    labelChanged: css`
        font-weight: 700;
    `,
});
