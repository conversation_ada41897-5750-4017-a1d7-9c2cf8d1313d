import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderEntry } from '../../types';
import { OrderbookHeader } from './OrderbookHeader';
import { OrderSide } from './OrderSide';

interface OrderbookTableProps {
    buyOrders: OrderEntry[];
    sellOrders: OrderEntry[];
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    highlightOpenOrders?: boolean;
    enableAnimations?: boolean;
    tooltip: {
        visible: boolean;
        x: number;
        y: number;
        side: 'buy' | 'sell';
        index: number;
    };
    onMouseEnter: (side: 'buy' | 'sell', index: number, event: React.MouseEvent) => void;
    onMouseLeave: () => void;
    isMarketSnapshot?: boolean;
}

export const OrderbookTable: React.FC<OrderbookTableProps> = ({
                                                                  buyOrders,
                                                                  sellOrders,
                                                                  decimalPlaces,
                                                                  tooltip,
                                                                  onMouseEnter,
                                                                  onMouseLeave,
                                                                  isMarketSnapshot = false,
                                                              }) => {
    const styles = useStyles2(getStyles);

    return (
        <div className={styles.orderbookContainer}>
            <OrderbookHeader isMarketSnapshot={isMarketSnapshot} />

            <div className={styles.orderbookContent}>
                {/* Buy Orders (Left Side) */}
                <OrderSide
                    orders={buyOrders}
                    side="buy"
                    decimalPlaces={decimalPlaces}
                    tooltip={tooltip}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    isMarketSnapshot={isMarketSnapshot}
                />

                {/* Sell Orders (Right Side) */}
                <OrderSide
                    orders={sellOrders}
                    side="sell"
                    decimalPlaces={decimalPlaces}
                    tooltip={tooltip}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    isMarketSnapshot={isMarketSnapshot}
                />
            </div>
        </div>
    );
};

const getStyles = () => ({
    orderbookContainer: css`
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
    `,
    orderbookContent: css`
        display: flex;
        flex: 1;
        overflow: hidden;
    `,
});
