import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';

interface OrderbookHeaderProps {
    isMarketSnapshot?: boolean;
}

export const OrderbookHeader: React.FC<OrderbookHeaderProps> = ({ isMarketSnapshot }) => {
    const styles = useStyles2(getStyles);

    return (
        <div className={styles.headerContainer}>
            {/* Buy Side Headers */}
            <div className={styles.buySide}>
                {isMarketSnapshot ? (
                    <>
                        <div className={styles.snapshotTotalCell}>Total</div>
                        <div className={styles.snapshotAmountCell}>Amount</div>
                        <div className={styles.snapshotPriceCell}>Price</div>
                    </>
                ) : (
                    <>
                        <div className={styles.spreadCell}>Top</div>
                        <div className={styles.spreadCell}>Ref</div>
                        <div className={styles.amountCell}>Amount</div>
                        <div className={styles.priceCell}>Price</div>
                    </>
                )}
            </div>

            {/* Sell Side Headers */}
            <div className={styles.sellSide}>
                {isMarketSnapshot ? (
                    <>
                        <div className={styles.snapshotPriceCell}>Price</div>
                        <div className={styles.snapshotAmountCell}>Amount</div>
                        <div className={styles.sellSnapshotTotalCell}>Total</div>
                    </>
                ) : (
                    <>
                        <div className={styles.priceCell}>Price</div>
                        <div className={styles.amountCell}>Amount</div>
                        <div className={styles.spreadCell}>Ref</div>
                        <div className={styles.spreadCell}>Top</div>
                    </>
                )}
            </div>
        </div>
    );
};

const getStyles = () => ({
    headerContainer: css`
        display: flex;
        background-color: #121722;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    `,
    buySide: css`
        display: flex;
        flex: 1;
        padding: 8px 0px;
        margin-right: 13px;
    `,
    sellSide: css`
        display: flex;
        flex: 1;
        padding: 8px 0px;
        margin-right: 13px;
    `,
    spreadCell: css`
        flex: 1;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
    `,
    totalCell: css`
        flex: 1;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
    `,
    amountCell: css`
        flex: 1;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
    `,
    priceCell: css`
        flex: 1.5;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        padding: 0 6px;
        min-width: 90px;
    `,
    snapshotTotalCell: css`
        flex: 1;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: right;
        padding: 0 4px;
    `,
    sellSnapshotTotalCell: css`
        flex: 1;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        padding: 0px 19px 0px 13px;
    `,
    snapshotAmountCell: css`
        flex: 1;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: right;
        padding: 0 4px;
    `,
    snapshotPriceCell: css`
        flex: 1.5;
        color: #9096a1;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        padding: 0 4px;
        min-width: 90px;
    `,
});
