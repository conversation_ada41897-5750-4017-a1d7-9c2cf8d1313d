import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderEntry } from '../../types';
import { OrderRow } from './OrderRow';

interface OrderSideProps {
    orders: OrderEntry[];
    side: 'buy' | 'sell';
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    highlightOpenOrders?: boolean;
    enableAnimations?: boolean;
    tooltip: {
        visible: boolean;
        x: number;
        y: number;
        side: 'buy' | 'sell';
        index: number;
    };
    onMouseEnter: (side: 'buy' | 'sell', index: number, event: React.MouseEvent) => void;
    onMouseLeave: () => void;
    isMarketSnapshot?: boolean;
}

export const OrderSide: React.FC<OrderSideProps> = ({
                                                        orders,
                                                        side,
                                                        decimalPlaces,
                                                        tooltip,
                                                        onMouseEnter,
                                                        onMouseLeave,
                                                        isMarketSnapshot = false,
                                                    }) => {
    const styles = useStyles2(getStyles);

    return (
        <div className={styles.sideContainer}>
            {orders.map((order, index) => (
                <OrderRow
                    key={`${side}-${index}`}
                    order={order}
                    index={index}
                    side={side}
                    isSelected={tooltip.visible && tooltip.side === side && index === tooltip.index}
                    isHovered={tooltip.visible && tooltip.side === side && index < tooltip.index}
                    orders={orders}
                    decimalPlaces={decimalPlaces}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    isMarketSnapshot={isMarketSnapshot}
                />
            ))}
        </div>
    );
};

const getStyles = () => ({
    sideContainer: css`
        flex: 1;
        overflow-y: auto;
        border-right: 1px solid rgba(255, 255, 255, 0.05);
    `,
});
