import React, { useState, useEffect, useRef } from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderEntry } from '../../types';
import { formatPrice, formatAmount } from '../../utils/formatters';
import { DepthVisualization } from './DepthVisualization';

interface OrderRowProps {
    order: OrderEntry;
    index: number;
    side: 'buy' | 'sell';
    isSelected: boolean;
    isHovered: boolean;
    orders: OrderEntry[];
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    onMouseEnter: (side: 'buy' | 'sell', index: number, event: React.MouseEvent) => void;
    onMouseLeave: () => void;
    isMarketSnapshot?: boolean;
}

export const OrderRow: React.FC<OrderRowProps> = ({
                                                      order,
                                                      index,
                                                      side,
                                                      isSelected,
                                                      isHovered,
                                                      orders,
                                                      decimalPlaces,
                                                      onMouseEnter,
                                                      onMouseLeave,
                                                      isMarketSnapshot = false,
                                                  }) => {
    const styles = useStyles2(getStyles);

    // Track previous price for animation
    const prevPriceRef = useRef<number>(order.price);
    const [priceChanged, setPriceChanged] = useState(false);
    const [priceDirection, setPriceDirection] = useState<'up' | 'down' | 'none'>('none');

    // Track amount changes
    const prevAmountRef = useRef<number>(order.amount);
    const [amountChanged, setAmountChanged] = useState(false);

    // Track if this is a new order
    const [isNewOrder, setIsNewOrder] = useState(false);

    // Use effect to detect changes and trigger animations
    useEffect(() => {
        // Check if this is a brand new order
        if (prevPriceRef.current === 0) {
            setIsNewOrder(true);
            setTimeout(() => setIsNewOrder(false), 1000);
        }
        // Price change detection
        else if (order.price !== prevPriceRef.current) {
            setPriceChanged(true);
            setPriceDirection(order.price > prevPriceRef.current ? 'up' : 'down');
            setTimeout(() => setPriceChanged(false), 1000);
        }

        // Amount change detection
        if (prevAmountRef.current !== 0 && order.amount !== prevAmountRef.current) {
            setAmountChanged(true);
            setTimeout(() => setAmountChanged(false), 1000);
        }

        // Update refs for next comparison
        prevPriceRef.current = order.price;
        prevAmountRef.current = order.amount;
    }, [order.price, order.amount]);

    // Determine which row style to use based on side, hover state, and open order status
    const rowClassName = cx(
        side === 'buy' ? styles.buyRow : styles.sellRow,
        isHovered ? (side === 'buy' ? styles.hoveredBuyRow : styles.hoveredSellRow) : '',
        isSelected ? styles.lastSelectedRow : '',
        order.hasOpenOrder ? styles.hasOpenOrder : '',
        isNewOrder && (side === 'buy' ? styles.newBuyOrder : styles.newSellOrder)
    );

    // Format spread as percentage
    const formatSpread = (spread?: number) => {
        if (spread === undefined || spread === null) return '-'.padStart(8, ' ');
        return (spread * 100).toFixed(3).padStart(7, ' ') + '%';
    };

    // Format total value
    const formatTotal = (total?: number) => {
        if (total === undefined || total === null) return '-';
        return formatAmount(total, decimalPlaces.total);
    };

    return (
        <div
            className={rowClassName}
            onMouseEnter={(e) => onMouseEnter(side, index, e)}
            onMouseLeave={onMouseLeave}
        >
            {/* Depth Visualization Background */}
            <DepthVisualization
                order={order}
                orders={orders}
                side={side}
            />

            {/* Cell Content - Rendered differently based on side and data source */}
            {side === 'buy' ? (
                // Buy Order Row Format
                isMarketSnapshot ? (
                    // Market snapshot format for buy: Total | Amount | Price
                    <>
                        <div className={styles.snapshotTotalCell}>
                            {formatTotal(order.total)}
                        </div>
                        <div className={cx(styles.cell, styles.amountCell, amountChanged && styles.valueChanged)}>
                            {formatAmount(order.amount, decimalPlaces.amount)}
                        </div>
                        <div className={cx(
                            styles.cell,
                            styles.priceCell,
                            styles.buyPrice,
                            priceChanged && (priceDirection === 'up' ? styles.buyPriceUp : styles.buyPriceDown)
                        )}>
                            {formatPrice(order.price, decimalPlaces.price)}
                        </div>
                    </>
                ) : (
                    // Trading data format: Top Spread | Ref Spread | Amount | Price
                    <>
                        <div className={styles.spreadCell}>
                            {formatSpread(order.topSpread)}
                        </div>
                        <div className={styles.spreadCell}>
                            {formatSpread(order.refSpread)}
                        </div>
                        <div className={cx(styles.cell, styles.amountCell, amountChanged && styles.valueChanged)}>
                            {formatAmount(order.amount, decimalPlaces.amount)}
                        </div>
                        <div className={cx(
                            styles.cell,
                            styles.priceCell,
                            styles.buyPrice,
                            priceChanged && (priceDirection === 'up' ? styles.buyPriceUp : styles.buyPriceDown)
                        )}>
                            {formatPrice(order.price, decimalPlaces.price)}
                        </div>
                    </>
                )
            ) : (
                // Sell Order Row Format
                isMarketSnapshot ? (
                    // Market snapshot format for sell: Price | Amount | Total
                    <>
                        <div className={cx(
                            styles.cell,
                            styles.priceCell,
                            styles.sellPrice,
                            priceChanged && (priceDirection === 'up' ? styles.sellPriceUp : styles.sellPriceDown)
                        )}>
                            {formatPrice(order.price, decimalPlaces.price)}
                        </div>
                        <div className={cx(styles.cell, styles.amountCell, amountChanged && styles.valueChanged)}>
                            {formatAmount(order.amount, decimalPlaces.amount)}
                        </div>
                        <div className={styles.sellSnapshotTotalCell}>
                            {formatTotal(order.total)}
                        </div>
                    </>
                ) : (
                    // Trading data format: Price | Amount | Ref Spread | Top Spread
                    <>
                        <div className={cx(
                            styles.cell,
                            styles.priceCell,
                            styles.sellPrice,
                            priceChanged && (priceDirection === 'up' ? styles.sellPriceUp : styles.sellPriceDown)
                        )}>
                            {formatPrice(order.price, decimalPlaces.price)}
                        </div>
                        <div className={cx(styles.cell, styles.amountCell, amountChanged && styles.valueChanged)}>
                            {formatAmount(order.amount, decimalPlaces.amount)}
                        </div>
                        <div className={styles.spreadCell}>
                            {formatSpread(order.refSpread)}
                        </div>
                        <div className={styles.spreadCell}>
                            {formatSpread(order.topSpread)}
                        </div>
                    </>
                )
            )}

            {/* Indicator for open orders */}
            {order.hasOpenOrder && (
                <div className={styles.openOrderIndicator} title="Your open order">
                    ●
                </div>
            )}
        </div>
    );
};

const getStyles = () => ({
    buyRow: css`
        display: flex;
        padding: 4px 12px;
        position: relative;
        cursor: pointer;
    `,
    sellRow: css`
        display: flex;
        padding: 4px 12px;
        position: relative;
        cursor: pointer;
    `,
    hoveredBuyRow: css`
        background-color: rgba(100, 100, 100, 0.15);
    `,
    hoveredSellRow: css`
        background-color: rgba(100, 100, 100, 0.15);
    `,
    lastSelectedRow: css`
        position: relative;
        background-color: rgba(100, 100, 100, 0.15) !important;
        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
        }
    `,
    hasOpenOrder: css`
        position: relative;
        border-left: 3px solid #ffb100;
        background-color: rgba(255, 177, 0, 0.1);
    `,
    newBuyOrder: css`
        animation: flashNewBuy 1s ease-out;
        @keyframes flashNewBuy {
            0% { background-color: rgba(0, 177, 100, 0.3); }
            100% { background-color: transparent; }
        }
    `,
    newSellOrder: css`
        animation: flashNewSell 1s ease-out;
        @keyframes flashNewSell {
            0% { background-color: rgba(234, 0, 112, 0.3); }
            100% { background-color: transparent; }
        }
    `,
    openOrderIndicator: css`
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        color: #ffb100;
        font-size: 10px;
    `,
    cell: css`
        flex: 1;
        position: relative;
        z-index: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: right;
    `,
    priceCell: css`
        flex: 1.5; // Increase from 1 to 1.5 to give more space to price columns
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 90px; // Ensure minimum width for small decimals
    `,
    amountCell: css`
        text-align: right;
    `,
    spreadCell: css`
        flex: 1;
        position: relative;
        z-index: 1;
        align-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
        font-size: 11px;
        color: #9096a1;
    `,
    snapshotTotalCell: css`
        flex: 1;
        position: relative;
        z-index: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: right;
        font-size: 14px;
        color: #ffffff;
        padding: 0 4px;
    `,
    sellSnapshotTotalCell: css`
        flex: 1;
        position: relative;
        z-index: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: right;
        font-size: 14px;
        color: #ffffff;
        padding-right: 16px;
    `,
    buyPrice: css`
        color: #00b164;
    `,
    sellPrice: css`
        color: #ea0070;
    `,
    buyPriceUp: css`
        animation: pulseBuyUp 1s ease-out;
        @keyframes pulseBuyUp {
            0% { background-color: rgba(0, 177, 100, 0.3); color: white; font-weight: bold; }
            100% { background-color: transparent; }
        }
    `,
    buyPriceDown: css`
        animation: pulseBuyDown 1s ease-out;
        @keyframes pulseBuyDown {
            0% { background-color: rgba(234, 0, 112, 0.2); }
            100% { background-color: transparent; }
        }
    `,
    sellPriceUp: css`
        animation: pulseSellUp 1s ease-out;
        @keyframes pulseSellUp {
            0% { background-color: rgba(0, 177, 100, 0.2); }
            100% { background-color: transparent; }
        }
    `,
    sellPriceDown: css`
        animation: pulseSellDown 1s ease-out;
        @keyframes pulseSellDown {
            0% { background-color: rgba(234, 0, 112, 0.3); color: white; font-weight: bold; }
            100% { background-color: transparent; }
        }
    `,
    valueChanged: css`
        animation: pulseValueChanged 1s ease-out;
        @keyframes pulseValueChanged {
            0% { background-color: rgba(255, 255, 255, 0.1); }
            100% { background-color: transparent; }
        }
    `,
});
