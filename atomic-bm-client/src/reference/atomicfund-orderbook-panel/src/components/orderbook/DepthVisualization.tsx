import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderEntry } from '../../types';

interface DepthVisualizationProps {
    order: OrderEntry;
    orders: OrderEntry[];
    side: 'buy' | 'sell';
}

/**
 * Component that renders the depth bar visualization for each order row
 * Enhanced with gradient effects and dynamic opacity
 * Updated to use individual amount values for depth visualization
 */
export const DepthVisualization: React.FC<DepthVisualizationProps> = ({
                                                                          order,
                                                                          orders,
                                                                          side,
                                                                      }) => {
    const styles = useStyles2(getStyles);

    // Calculate the width percentage for visualization based on individual order amount
    const depthWidth = calculateDepthWidth(orders, order, side);

    // Calculate dynamic opacity based on the relative width
    // This creates a visual effect where larger orders appear more prominent
    const opacity = Math.max(0.2, Math.min(0.8, depthWidth / 100 + 0.2));

    return (
        <div
            className={cx(
                styles.depthVisualization,
                side === 'buy' ? styles.buyDepth : styles.sellDepth
            )}
            style={{
                width: `${depthWidth}%`,
                opacity: opacity
            }}
        />
    );
};

/**
 * Calculate the width percentage for depth visualization based on individual order amount
 * This shows the relative size of each order directly, making it easier to identify
 * large orders in the orderbook
 */
function calculateDepthWidth(
    orders: OrderEntry[],
    currentOrder: OrderEntry,
    side: 'buy' | 'sell'
): number {
    if (orders.length === 0) {
        return 0;
    }

    // Find the maximum amount in all orders to use as the base for percentage calculation
    const maxAmount = Math.max(...orders.map(order => order.amount));

    if (maxAmount <= 0) {
        return 0;
    }

    // Calculate percentage directly based on the current order's amount
    const percentage = (currentOrder.amount / maxAmount) * 100;

    // Return the percentage, ensuring it's within 0-100% range
    return Math.min(Math.max(percentage, 0), 100);
}

const getStyles = () => ({
    depthVisualization: css`
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 0;
    transition: width 0.3s ease, opacity 0.3s ease;
  `,
    buyDepth: css`
    background: linear-gradient(to left, rgba(0, 177, 100, 0.3), rgba(0, 177, 100, 0.15));
    right: 0;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
  `,
    sellDepth: css`
    background: linear-gradient(to right, rgba(234, 0, 112, 0.3), rgba(234, 0, 112, 0.15));
    left: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
  `,
});