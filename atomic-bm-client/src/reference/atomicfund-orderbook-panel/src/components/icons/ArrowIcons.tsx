import React from 'react';

export const UpArrowIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 2.999l7.071 7.071-1.768 1.768-4.054-4.055V21h-2.5V7.784l-4.053 4.054-1.768-1.768 7.07-7.071H12z"
            fill="currentColor"
        />
    </svg>
);

export const DownArrowIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 20.999l7.071-7.071-1.768-1.768-4.054 4.055V2.998h-2.5v13.216L6.696 12.16l-1.768 1.768 7.07 7.071H12z"
            fill="currentColor"
        />
    </svg>
);
