import React, { useState, useEffect, useRef } from 'react';
import { PanelProps } from '@grafana/data';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderbookOptions } from '../types';

// Import components and hooks
import { useOrderbookData } from '../hooks/useOrderbookData';
import { usePlayback } from '../hooks/usePlayback';
import { useTooltip } from '../hooks/useTooltip';
import { useTimeSeries } from '../hooks/useTimeSeries';
import { OrderbookTable } from './orderbook/OrderbookTable';
import { SummaryBar } from './summary/SummaryBar';
import { RatioVisualization } from './summary/RatioVisualization';
import { PlaybackControls } from './controls/PlaybackControls';
import { OrderbookTimeSeries } from './timeseries/OrderbookTimeSeries';
import { OrderTooltip } from './tooltips/OrderTooltip';
import { LiveIndicator } from './LiveIndicator';
import { TradingMetrics } from './TradingMetrics';
import { OrderbookTableMobile } from './mobile/OrderbookTableMobile';
import { TradingMetricsMobile } from './mobile/TradingMetricsMobile';
import { OrdersTableMobile } from './mobile/OrdersTableMobile';
import { RefMarketsPanel } from './RefMarketsPanel';
import { PositionsManagementPanel } from './OpenOrdersPanel';
import { ExecutionStatsPanel } from './ExecutionStatsPanel';
import { ExecutionStatsMobile } from './mobile/ExecutionStatsMobile';

type Props = PanelProps<OrderbookOptions>;

export const OrderbookPanel: React.FC<Props> = (props) => {
  const { options, data, width, height, timeRange, timeZone } = props;
  const styles = useStyles2(getStyles);

  // Check for completely empty data early to avoid rendering issues
  if (!data || !data.series || data.series.length === 0) {
    return (
        <div className={styles.wrapper} data-testid="orderbook-wrapper">
          <div className={styles.noDataMessage}>
            No data to display. Please configure your panel with appropriate data.
          </div>
        </div>
    );
  }

  // Track panel dimensions for responsive layout
  const [isCompactView, setIsCompactView] = useState(width < 500);
  const [isMobileView, setIsMobileView] = useState(width < 428);
  const [activeTab, setActiveTab] = useState<'orderbook' | 'metrics' | 'orders' | 'execstats'>('orderbook');
  const panelRef = useRef<HTMLDivElement>(null);

  // Update view state when panel resizes
  useEffect(() => {
    setIsCompactView(width < 500);
    setIsMobileView(width < 428);
  }, [width]);

  // Default options with tradingMetricsPosition always set to 'side'
  const panelOptions: OrderbookOptions = {
    ...options,
  };

  // View state - default to live data
  const [isLiveData, setIsLiveData] = useState(true);
  const [selectedTimestamp, setSelectedTimestamp] = useState<number | null>(null);

  // Get orderbook data using custom hook
  const {
    buyOrders,
    sellOrders,
    summary,
    timestamps,
    lastUpdateTime,
    hasTradingData,
    tradingData,
    isMarketSnapshot
  } = useOrderbookData(data.series, panelOptions, isLiveData, selectedTimestamp);

  // Check if we have valid data to display
  const hasValidData = buyOrders.length > 0 || sellOrders.length > 0;

  // Helper function to safely format a timestamp
  const formatTimestamp = (timestamp: number | null): string => {
    if (timestamp === null || isNaN(timestamp) || !isFinite(timestamp)) {
      return 'Unknown time';
    }

    try {
      return new Date(timestamp).toISOString().replace('T', ' ').substring(0, 19);
    } catch (e) {
      console.error('Error formatting timestamp:', timestamp, e);
      return 'Invalid timestamp';
    }
  };

  // Helper function to get a safe timestamp value
  const getSafeTimestamp = (): number => {
    if (selectedTimestamp !== null && isFinite(selectedTimestamp)) {
      return selectedTimestamp;
    }

    if (timestamps.length > 0 && isFinite(timestamps[timestamps.length - 1])) {
      return timestamps[timestamps.length - 1];
    }

    return Date.now(); // Fallback to current time
  };

  // Playback functionality
  const {
    isPlaying,
    currentTimestampIndex,
    playbackSpeed,
    handlePlay,
    handlePause,
    handleSpeedChange,
    handleSeek
  } = usePlayback(timestamps, isLiveData, (timestamp) => {
    if (timestamp === null) {
      setSelectedTimestamp(null);
      setIsLiveData(true);
    } else {
      // Add a safety check to ensure the timestamp is a valid number
      if (!isNaN(timestamp) && isFinite(timestamp)) {
        setSelectedTimestamp(timestamp);
        setIsLiveData(false);
      } else {
        console.error('Invalid timestamp received:', timestamp);
        // Fallback to live mode on invalid timestamp
        setSelectedTimestamp(null);
        setIsLiveData(true);
      }
    }
  });

  // Tooltip handling
  const {
    tooltip,
    handleMouseEnter,
    handleMouseLeave,
    calculateTooltipValues
  } = useTooltip(buyOrders, sellOrders);

  // Time series data
  const { timeSeriesFrames } = useTimeSeries(data.series);

  // Handle snapshot selection
  const handleSnapshotSelect = (timestamp: number) => {
    // Validate timestamp
    if (isNaN(timestamp) || !isFinite(timestamp)) {
      console.error('Invalid timestamp selected:', timestamp);
      return;
    }

    setIsLiveData(false);
    setSelectedTimestamp(timestamp);

    // Find the index of the selected timestamp
    const index = timestamps.findIndex(t => t === timestamp);
    if (index !== -1) {
      handleSeek(index);
    }
  };

  // Return to live data
  const handleReturnToLive = () => {
    if (isPlaying) {
      handlePause();
    }
    setSelectedTimestamp(null);
    setIsLiveData(true);
    if (timestamps.length > 0) {
      const latestIndex = timestamps.length - 1;
      handleSeek(latestIndex, true);
    }
  };

  // Calculate available content height
  const calculateContentHeight = () => {
    if (!panelRef.current) return 'auto';

    let totalHeight = height;

    // Subtract heights of other components
    if (panelOptions.enableTimeNavigation) {
      totalHeight -= 40; // Controls bar height
    }

    // Make sure we have enough room for content
    return `${Math.max(200, totalHeight)}px`;
  };

  // Render controls for navigation
  const renderControls = () => {
    if (!panelOptions.enableTimeNavigation) {
      return null;
    }

    return (
        <div className={styles.controlsContainer}>
          <LiveIndicator isLive={isLiveData} />

          <div className={styles.statusInfo}>
            {isLiveData ? (
                <span>Last update: {lastUpdateTime || 'No updates yet'}</span>
            ) : (
                selectedTimestamp ? (
                    <span>Viewing snapshot from: {formatTimestamp(selectedTimestamp)}</span>
                ) : (
                    <span>Historical view</span>
                )
            )}
          </div>

          <div className={styles.buttonContainer}>
            <button
                className={styles.returnToLiveButton}
                onClick={handleReturnToLive}
                style={{ visibility: isLiveData ? 'hidden' : 'visible' }}
            >
              Return to Live
            </button>
          </div>
        </div>
    );
  };

  // Mobile-specific tab navigation
  const renderMobileTabs = () => {
    return (
        <div className={styles.mobileTabs}>
          <button
              className={activeTab === 'orderbook' ? styles.activeTab : styles.tab}
              onClick={() => setActiveTab('orderbook')}>
            Orderbook
          </button>
          <button
              className={activeTab === 'metrics' ? styles.activeTab : styles.tab}
              onClick={() => setActiveTab('metrics')}>
            Metrics
          </button>
          <button
              className={activeTab === 'orders' ? styles.activeTab : styles.tab}
              onClick={() => setActiveTab('orders')}>
            Orders
          </button>
          <button
              className={activeTab === 'execstats' ? styles.activeTab : styles.tab}
              onClick={() => setActiveTab('execstats')}>
            Stats
          </button>
        </div>
    );
  };

  // Mobile view renderer
  const renderMobileView = () => {
    return (
        <div className={styles.mobileWrapper}>
          {/* Controls for switching between live and historical views */}
          {renderControls()}

          {/* Tab navigation */}
          {renderMobileTabs()}

          {/* Tab content */}
          <div className={styles.mobileContent}>
            {activeTab === 'orderbook' && (
                <div className={styles.mobilePanelContent}>
                  <OrderbookTableMobile
                      buyOrders={buyOrders}
                      sellOrders={sellOrders}
                      decimalPlaces={panelOptions.decimalPlaces}
                  />

                  {/* Summary and ratio visualization */}
                  {panelOptions.showSummary && (
                      <>
                        <SummaryBar
                            summary={summary}
                            decimalPlaces={panelOptions.decimalPlaces}
                            tradingData={tradingData}
                            isMobile={true}
                        />
                        <RatioVisualization
                            buyPercentage={summary.buyPercentage}
                            sellPercentage={summary.sellPercentage}
                        />
                      </>
                  )}

                  {/* Playback controls */}
                  {panelOptions.enablePlayback && panelOptions.enableTimeNavigation && timestamps.length > 0 && (
                      <PlaybackControls
                          timestamps={timestamps}
                          isPlaying={isPlaying}
                          currentTimestampIndex={currentTimestampIndex}
                          playbackSpeed={playbackSpeed}
                          onPlay={handlePlay}
                          onPause={handlePause}
                          onSpeedChange={handleSpeedChange}
                          onSeek={handleSeek}
                          isMobile={true}
                      />
                  )}

                  {/* Time series chart */}
                  {panelOptions.enableTimeNavigation && timeSeriesFrames.length > 0 && (
                      <div className={styles.mobileTimeseriesContainer}>
                        <OrderbookTimeSeries
                            frames={timeSeriesFrames}
                            timeRange={timeRange}
                            timeZone={timeZone}
                            width={width - 20} // Account for padding
                            height={Math.min(120, height * 0.25)}
                            onSnapshotSelect={handleSnapshotSelect}
                            decimalPlaces={panelOptions.decimalPlaces}
                        />
                      </div>
                  )}
                </div>
            )}

            {activeTab === 'metrics' && !isMarketSnapshot && (
                <div className={styles.mobilePanelContent}>
                  <TradingMetricsMobile
                      tradingData={tradingData}
                      decimalPlaces={panelOptions.decimalPlaces}
                  />
                </div>
            )}

            {activeTab === 'orders' && !isMarketSnapshot && (
                <div className={styles.mobilePanelContent}>
                  <OrdersTableMobile
                      tradingData={tradingData}
                      decimalPlaces={panelOptions.decimalPlaces}
                      currentPrice={summary.avgPrice}
                      ts={getSafeTimestamp()}
                  />
                </div>
            )}

            {activeTab === 'execstats' && !isMarketSnapshot && (
                <div className={styles.mobilePanelContent}>
                  <ExecutionStatsMobile
                      tradingData={tradingData}
                      decimalPlaces={panelOptions.decimalPlaces}
                  />
                </div>
            )}
          </div>
        </div>
    );
  };

  // Desktop view renderer
  const renderDesktopView = () => {
    return (
        <>
          {/* Controls for switching between live and historical views */}
          {renderControls()}

          {/* Main content with layout based on data source */}
          <div className={styles.horizontalLayout} style={{ height: calculateContentHeight() }}>
            {/* Left side metrics container - hide for market snapshots */}
            {!isMarketSnapshot && hasTradingData && panelOptions.showTradingMetrics && (
                <div className={styles.leftMetricsContainer}>
                  <TradingMetrics
                      tradingData={tradingData}
                      decimalPlaces={panelOptions.decimalPlaces}
                      showCompactView={isCompactView}
                  />
                </div>
            )}

            {/* Center column for orderbook - adjust width based on data source */}
            <div className={cx(
                styles.centerContainer,
                isMarketSnapshot && styles.fullWidthContainer
            )}>
              {/* Main orderbook table */}
              <OrderbookTable
                  buyOrders={buyOrders}
                  sellOrders={sellOrders}
                  decimalPlaces={panelOptions.decimalPlaces}
                  tooltip={tooltip}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  isMarketSnapshot={isMarketSnapshot}
              />

              {/* Summary and ratio visualization */}
              {panelOptions.showSummary && (
                  <>
                    <SummaryBar
                        summary={summary}
                        decimalPlaces={panelOptions.decimalPlaces}
                        tradingData={tradingData}
                    />
                    <RatioVisualization
                        buyPercentage={summary.buyPercentage}
                        sellPercentage={summary.sellPercentage}
                    />
                  </>
              )}

              {/* Playback controls */}
              {panelOptions.enablePlayback && panelOptions.enableTimeNavigation && timestamps.length > 0 && (
                  <PlaybackControls
                      timestamps={timestamps}
                      isPlaying={isPlaying}
                      currentTimestampIndex={currentTimestampIndex}
                      playbackSpeed={playbackSpeed}
                      onPlay={handlePlay}
                      onPause={handlePause}
                      onSpeedChange={handleSpeedChange}
                      onSeek={handleSeek}
                  />
              )}

              {/* Time series chart */}
              {panelOptions.enableTimeNavigation && timeSeriesFrames.length > 0 && (
                  <div>
                    <OrderbookTimeSeries
                        frames={timeSeriesFrames}
                        timeRange={timeRange}
                        timeZone={timeZone}
                        width={isMarketSnapshot ? width : 735}
                        height={Math.min(150, height * 0.3)}
                        onSnapshotSelect={handleSnapshotSelect}
                        decimalPlaces={panelOptions.decimalPlaces}
                    />
                  </div>
              )}
            </div>

            {/* Right side container for analytics - hide for market snapshots */}
            {!isMarketSnapshot && (
                <div className={styles.rightAnalyticsContainer}>
                  {/* Execution Stats Panel */}
                  {hasTradingData && tradingData && tradingData.exec_stats && tradingData.exec_stats.length > 0 && (
                      <ExecutionStatsPanel
                          tradingData={tradingData}
                          decimalPlaces={panelOptions.decimalPlaces}
                      />
                  )}

                  {/* Positions Management Panel */}
                  {hasTradingData && tradingData && (
                      <PositionsManagementPanel
                          tradingData={tradingData}
                          decimalPlaces={panelOptions.decimalPlaces}
                          currentPrice={summary.avgPrice}
                          ts={getSafeTimestamp()}
                      />
                  )}

                  {/* Reference Markets Panel */}
                  {hasTradingData && tradingData && tradingData.ref_markets && tradingData.ref_markets.length > 0 && (
                      <RefMarketsPanel
                          tradingData={tradingData}
                          decimalPlaces={panelOptions.decimalPlaces}
                      />
                  )}
                </div>
            )}
          </div>
        </>
    );
  };

  return (
      <div className={styles.wrapper} data-testid="orderbook-wrapper" ref={panelRef}>
        {!hasValidData ? (
            <div className={styles.noDataMessage}>
              No orderbook data to display. Please check your query or data source.
            </div>
        ) : isMobileView ? (
            renderMobileView()
        ) : (
            renderDesktopView()
        )}

        {/* Tooltip - only show on desktop */}
        {!isMobileView && tooltip.visible && hasValidData && (
            <OrderTooltip
                visible={tooltip.visible}
                x={tooltip.x}
                y={tooltip.y}
                avgPrice={calculateTooltipValues(tooltip.side, tooltip.index).avgPrice}
                sumBaseAmount={calculateTooltipValues(tooltip.side, tooltip.index).sumBaseAmount}
                sumQuoteValue={calculateTooltipValues(tooltip.side, tooltip.index).sumQuoteValue}
                decimalPlaces={panelOptions.decimalPlaces}
            />
        )}
      </div>
  );
};

// Define cx function since it might not be imported
const cx = (...args: any[]) => {
  return args.filter(Boolean).join(' ');
};

// Updated styles definition with mobile support
const getStyles = () => ({
  wrapper: css`
    font-family: 'Roboto', 'Open Sans', sans-serif;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #0b0f19;
    color: #fff;
    overflow: hidden;
  `,
  horizontalLayout: css`
    display: flex;
    flex-direction: row;
    flex: 1;
    overflow: hidden;
  `,
  leftMetricsContainer: css`
    width: 250px;
    min-width: 250px;
    height: 100%;
    border-right: 1px solid rgba(255, 255, 255, 0.05);
    background-color: #121722;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
  `,
  centerContainer: css`
    display: flex;
    flex-direction: column;
    flex: 2;
    max-width: 735px;
    min-width: 735px;
    overflow-y: auto;
    border-right: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
  `,
  fullWidthContainer: css`
    max-width: 100% !important;
    min-width: 100% !important;
    border-right: none !important;
  `,
  rightAnalyticsContainer: css`
    flex: 1;
    min-width: 350px;
    height: 100%;
    background-color: #121722;
    overflow-y: visible;
    display: flex;
    flex-direction: column;
  `,
  controlsContainer: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    background-color: #121722;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 40px;
    width: 100%;
  `,
  statusInfo: css`
    font-size: 11px;
    color: #9096a1;
    flex: 1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 8px;
  `,
  buttonContainer: css`
    min-width: 90px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  `,
  returnToLiveButton: css`
    background-color: rgba(0, 177, 100, 0.2);
    color: #00b164;
    border: 1px solid #00b164;
    border-radius: 4px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    
    &:hover {
      background-color: rgba(0, 177, 100, 0.3);
    }
    
    &:active {
      background-color: rgba(0, 177, 100, 0.4);
    }
  `,
  noDataMessage: css`
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9096a1;
    text-align: center;
    padding: 20px;
    font-size: 14px;
  `,
  // Mobile specific styles
  mobileWrapper: css`
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  `,
  mobileTabs: css`
    display: flex;
    background-color: #121722;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  `,
  tab: css`
    flex: 1;
    padding: 10px;
    text-align: center;
    background: transparent;
    color: #9096a1;
    border: none;
    font-size: 14px;
    cursor: pointer;
  `,
  activeTab: css`
    flex: 1;
    padding: 10px;
    text-align: center;
    background: transparent;
    color: white;
    border: none;
    font-size: 14px;
    border-bottom: 2px solid #5794F2;
    cursor: pointer;
  `,
  mobileContent: css`
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  `,
  mobilePanelContent: css`
    flex: 1;
    display: flex;
    flex-direction: column;
  `,
  mobileTimeseriesContainer: css`
    margin-top: 10px;
    padding: 0 10px;
  `,
});