import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice } from '../utils/formatters';

interface RefMarket {
    market_id: string;
    buy_price: number;
    sell_price: number;
    fx_market_id: string;
    fx_rate: number;
    last_updated: string;
}

interface RefMarketsPanelProps {
    tradingData: any;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
}

export const RefMarketsPanel: React.FC<RefMarketsPanelProps> = ({
                                                                    tradingData,
                                                                    decimalPlaces,
                                                                }) => {
    const styles = useStyles2(getStyles);

    if (!tradingData || !tradingData.ref_markets || tradingData.ref_markets.length === 0) {
        return (
            <div className={styles.container}>
                <div className={styles.header}>
                    <span>REFERENCE MARKETS</span>
                </div>
                <div className={styles.noData}>No reference market data available.</div>
            </div>
        );
    }

    // Parse market ID into exchange and pair
    const parseMarketId = (marketId: string) => {
        if (!marketId || marketId === ":") return { exchange: "-", pair: "-" };

        const parts = marketId.split(':');
        return {
            exchange: parts[0] || "-",
            pair: parts.length > 1 ? parts[1] : "-"
        };
    };

    // Calculate spread percentage between buy and sell prices
    const calculateSpread = (buy: number, sell: number): number => {
        if (!buy || !sell || buy === 0) return 0;
        return ((sell - buy) / buy) * 100;
    };

    // Find the reference market with the best buy price
    const bestBuyMarket = tradingData.ref_markets.reduce(
        (best: RefMarket | null, current: RefMarket) =>
            !best || (current.buy_price > best.buy_price) ? current : best,
        null
    );

    // Find the reference market with the best sell price
    const bestSellMarket = tradingData.ref_markets.reduce(
        (best: RefMarket | null, current: RefMarket) =>
            !best || (current.sell_price < best.sell_price) ? current : best,
        null
    );

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <span>REFERENCE MARKETS</span>
            </div>

            <div className={styles.tableWrapper}>
                <table className={styles.table}>
                    <thead>
                    <tr className={styles.headerRow}>
                        <th className={styles.colExchange}>Exchange</th>
                        <th className={styles.colPair}>Pair</th>
                        <th className={styles.colBuyPrice}>Buy Price</th>
                        <th className={styles.colSellPrice}>Sell Price</th>
                        <th className={styles.colSpread}>Spread</th>
                        <th className={styles.colFxPair}>FX Pair</th>
                        <th className={styles.colFxRate}>FX Rate</th>
                    </tr>
                    </thead>
                    <tbody>
                    {tradingData.ref_markets.map((market: RefMarket, index: number) => {
                        const { exchange, pair } = parseMarketId(market.market_id);
                        const { pair: fxPair } = parseMarketId(market.fx_market_id);
                        const spread = calculateSpread(market.buy_price, market.sell_price);
                        const isBestBuy = bestBuyMarket && market.market_id === bestBuyMarket.market_id;
                        const isBestSell = bestSellMarket && market.market_id === bestSellMarket.market_id;

                        return (
                            <tr
                                key={`market-${index}`}
                                className={cx(
                                    styles.dataRow,
                                    index % 2 === 0 ? styles.evenRow : ''
                                )}
                            >
                                <td className={styles.colExchange}>
                                    <span className={styles.exchangeBadge}>{exchange}</span>
                                </td>
                                <td className={styles.colPair}>{pair}</td>
                                <td className={styles.colBuyPrice}>
                                    <span className={cx(styles.buyPrice)}>
                                        {formatPrice(market.buy_price, decimalPlaces.price)}
                                        {isBestBuy && <span className={styles.bestMarker}>•</span>}
                                    </span>
                                </td>
                                <td className={styles.colSellPrice}>
                                    <span className={cx(styles.sellPrice)}>
                                        {formatPrice(market.sell_price, decimalPlaces.price)}
                                        {isBestSell && <span className={styles.bestMarker}>•</span>}
                                    </span>
                                </td>
                                <td className={styles.colSpread}>
                                    {spread.toFixed(3)}%
                                </td>
                                <td className={styles.colFxPair}>
                                    {fxPair !== "-" ? fxPair : "-"}
                                </td>
                                <td className={styles.colFxRate}>
                                    {market.fx_rate > 0 ? market.fx_rate.toFixed(3) : "-"}
                                </td>
                            </tr>
                        );
                    })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const getStyles = () => {
    return {
        container: css`
            margin: 10px;
            background-color: #121722;
            border-radius: 4px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            flex: 1; // Allow it to grow within parent container
            min-height: 0; // Critical for flexbox to respect scrolling!
            margin-bottom: 10px;
        `,
        header: css`
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            background-color: #1a202c;
            color: #9096a1;
            font-size: 12px;
            font-weight: 500;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            flex-shrink: 0; // Prevent header from shrinking
        `,
        tableWrapper: css`
            overflow-y: auto;
            flex: 1; // Allow table to take remaining space
            min-height: 0; // Critical for scroll to work in flexbox!
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        `,
        table: css`
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        `,
        headerRow: css`
            position: sticky;
            top: 0;
            background-color: #1a202c;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #9096a1;
            font-weight: 500;
            font-size: 11px;
            z-index: 2;
            
            & th {
                padding: 8px 10px;
                white-space: nowrap;
                position: relative;
                
                &:after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 25%;
                    height: 50%;
                    width: 1px;
                    background-color: rgba(255, 255, 255, 0.05);
                }
                
                &:last-child:after {
                    display: none;
                }
            }
        `,
        dataRow: css`
            border-bottom: 1px solid rgba(255, 255, 255, 0.03);
            transition: background-color 0.2s;
            
            &:hover {
                background-color: rgba(87, 148, 242, 0.05) !important;
            }
            
            & td {
                padding: 6px 10px;
                vertical-align: middle;
            }
        `,
        evenRow: css`
            background-color: rgba(26, 32, 44, 0.3);
        `,
        colExchange: css`
            width: 100px;
            text-align: left;
        `,
        colPair: css`
            width: 120px;
            text-align: left;
        `,
        colBuyPrice: css`
            width: 100px;
            text-align: left;
        `,
        colSellPrice: css`
            width: 100px;
            text-align: left;
        `,
        colSpread: css`
            width: 80px;
            text-align: left;
        `,
        colFxPair: css`
            width: 100px;
            text-align: left;
        `,
        colFxRate: css`
            width: 80px;
            text-align: left;
        `,
        exchangeBadge: css`
            display: inline-block;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 11px;
            color: #ffffff;
            text-transform: uppercase;
        `,
        buyPrice: css`
            color: #00b164;
        `,
        sellPrice: css`
            color: #ea0070;
        `,
        bestMarker: css`
            font-size: 14px;
            margin-left: 4px;
            color: #ffb100;
        `,
        noData: css`
            padding: 30px;
            text-align: center;
            color: #9096a1;
            font-size: 14px;
        `,
    };
};
