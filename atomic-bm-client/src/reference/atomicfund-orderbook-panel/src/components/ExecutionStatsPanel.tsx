import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount, formatTotal } from '../utils/formatters';

interface ExecStat {
    buy: {
        avg_price: number;
        money_volume: number;
        volume: number;
    };
    sell: {
        avg_price: number;
        money_volume: number;
        volume: number;
    };
    time_frame: number;
}

interface ExecutionStatsPanelProps {
    tradingData: any;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
}

// Helper function to format time frames
const formatTimeFrame = (nanoseconds: number): string => {
    // Convert nanoseconds to seconds
    const seconds = nanoseconds / 1000000000;

    if (seconds === 300) return '5m';
    if (seconds === 3600) return '1h';
    if (seconds === 43200) return '12h';
    if (seconds === 86400) return '24h';
    if (seconds === 604800) return '7d';

    // Fallback for unknown time frames
    return `${seconds}s`;
};

export const ExecutionStatsPanel: React.FC<ExecutionStatsPanelProps> = ({
                                                                            tradingData,
                                                                            decimalPlaces,
                                                                        }) => {
    const styles = useStyles2(getStyles);

    if (!tradingData || !tradingData.exec_stats || !Array.isArray(tradingData.exec_stats) || tradingData.exec_stats.length === 0) {
        return (
            <div className={styles.container}>
                <div className={styles.header}>
                    <span>EXECUTION STATS</span>
                </div>
                <div className={styles.noData}>No execution statistics available.</div>
            </div>
        );
    }

    // Sort stats by time frame (ascending)
    const sortedStats = [...tradingData.exec_stats].sort((a, b) => a.time_frame - b.time_frame);

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <span>EXECUTION STATS</span>
            </div>

            <div className={styles.tableWrapper}>
                <table className={styles.table}>
                    <thead>
                    <tr className={styles.headerRow}>
                        <th className={styles.timeframeColumn}>Timeframe</th>
                        <th className={styles.volumeColumn}>Volume</th>
                        <th className={styles.priceColumn} colSpan={2}>Avg Bid ↔ Avg Ask</th>
                        <th className={styles.spreadColumn}>Spread</th>
                        <th className={styles.positionColumn}>Net Position</th>
                        <th className={styles.pnlColumn}>PnL</th>
                    </tr>
                    </thead>
                    <tbody>
                    {sortedStats.map((stat: ExecStat, index: number) => {
                        const hasBuyData = stat.buy.volume > 0;
                        const hasSellData = stat.sell.volume > 0;

                        // Calculate total volume
                        const totalVolume = stat.buy.volume + stat.sell.volume;

                        // Calculate spread between avg ask and avg bid
                        let spread = 0;
                        let spreadPercent = 0;
                        if (hasBuyData && hasSellData && stat.buy.avg_price > 0) {
                            spread = stat.sell.avg_price - stat.buy.avg_price;
                            spreadPercent = (spread / stat.buy.avg_price) * 100;
                        }

                        // Calculate net position (buy volume - sell volume)
                        const netPosition = stat.buy.volume - stat.sell.volume;

                        // Calculate PnL estimate (sell value - buy value)
                        const buyValue = stat.buy.money_volume;
                        const sellValue = stat.sell.money_volume;
                        const pnl = sellValue - buyValue;

                        return (
                            <tr
                                key={`stat-${index}`}
                                className={cx(
                                    styles.dataRow,
                                    index % 2 === 0 ? styles.evenRow : '',
                                    (!hasBuyData && !hasSellData) && styles.emptyRow
                                )}
                            >
                                {/* Timeframe */}
                                <td className={styles.timeframeColumn}>
                                    {formatTimeFrame(stat.time_frame)}
                                </td>

                                {/* Total Volume */}
                                <td className={cx(styles.volumeColumn, styles.dataCell)}>
                                    {formatAmount(totalVolume, decimalPlaces.amount)}
                                </td>

                                {/* Avg Bid */}
                                <td className={cx(styles.bidColumn, styles.dataCell, !hasBuyData && styles.noData)}>
                                    {hasBuyData
                                        ? formatPrice(stat.buy.avg_price, decimalPlaces.price)
                                        : '-'}
                                </td>

                                {/* Avg Ask */}
                                <td className={cx(styles.askColumn, styles.dataCell, !hasSellData && styles.noData)}>
                                    {hasSellData
                                        ? formatPrice(stat.sell.avg_price, decimalPlaces.price)
                                        : '-'}
                                </td>

                                {/* Spread */}
                                <td className={cx(styles.spreadColumn, styles.dataCell)}>
                                    {(hasBuyData && hasSellData)
                                        ? `${spreadPercent.toFixed(2)}%`
                                        : '-'}
                                </td>

                                {/* Net Position */}
                                <td className={cx(
                                    styles.positionColumn,
                                    styles.dataCell,
                                    netPosition > 0 ? styles.buyValue : netPosition < 0 ? styles.sellValue : ''
                                )}>
                                    {formatAmount(Math.abs(netPosition), decimalPlaces.amount)}
                                    {netPosition !== 0 && (
                                        <span className={styles.positionIndicator}>
                                            {netPosition > 0 ? '↑' : '↓'}
                                        </span>
                                    )}
                                </td>

                                {/* PnL */}
                                <td className={cx(
                                    styles.pnlColumn,
                                    styles.dataCell,
                                    pnl > 0 ? styles.buyValue : pnl < 0 ? styles.sellValue : ''
                                )}>
                                    {formatTotal(Math.abs(pnl), decimalPlaces.total)}
                                    {pnl !== 0 && (
                                        <span className={styles.pnlIndicator}>
                                            {pnl > 0 ? '+' : '-'}
                                        </span>
                                    )}
                                </td>
                            </tr>
                        );
                    })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const getStyles = () => {
    return {
        container: css`
            margin: 10px;
            background-color: #121722;
            border-radius: 4px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            flex: 1;
            min-height: 238px;
            margin-bottom: 10px;
        `,
        header: css`
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            background-color: #1a202c;
            color: #9096a1;
            font-size: 12px;
            font-weight: 500;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            flex-shrink: 0;
        `,
        tableWrapper: css`
            overflow-y: auto;
            flex: 1;
            min-height: 0; 
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        `,
        table: css`
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        `,
        headerRow: css`
            position: sticky;
            top: 0;
            background-color: #1a202c;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #9096a1;
            font-weight: 500;
            font-size: 11px;
            z-index: 2;
            
            & th {
                padding: 8px 10px;
                text-align: center;
                vertical-align: middle;
                position: relative;
                
                &:after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 25%;
                    height: 50%;
                    width: 1px;
                    background-color: rgba(255, 255, 255, 0.05);
                }
                
                &:last-child:after {
                    display: none;
                }
            }
        `,
        dataRow: css`
            border-bottom: 1px solid rgba(255, 255, 255, 0.03);
            transition: background-color 0.2s;
            
            &:hover {
                background-color: rgba(87, 148, 242, 0.05) !important;
            }
            
            & td {
                padding: 6px 4px;
                vertical-align: middle;
                text-align: center;
            }
        `,
        emptyRow: css`
            opacity: 0.6;
        `,
        evenRow: css`
            background-color: rgba(26, 32, 44, 0.3);
        `,
        timeframeColumn: css`
            width: 70px;
            text-align: center;
            font-weight: 500;
        `,
        volumeColumn: css`
            width: 80px;
        `,
        priceColumn: css`
            width: 100px;
        `,
        bidColumn: css`
            color: #00b164;
            border-right: 1px dashed rgba(255, 255, 255, 0.2);
        `,
        askColumn: css`
            color: #ea0070;
        `,
        spreadColumn: css`
            width: 70px;
        `,
        positionColumn: css`
            width: 100px;
        `,
        pnlColumn: css`
            width: 90px;
        `,
        dataCell: css`
            font-variant-numeric: tabular-nums;
        `,
        noData: css`
            opacity: 0.5;
            padding: 30px;
            text-align: center;
            color: #9096a1;
            font-size: 14px;
        `,
        buyValue: css`
            color: #00b164;
        `,
        sellValue: css`
            color: #ea0070;
        `,
        positionIndicator: css`
            margin-left: 4px;
            font-size: 10px;
        `,
        pnlIndicator: css`
            margin-right: 2px;
        `,
    };
};
