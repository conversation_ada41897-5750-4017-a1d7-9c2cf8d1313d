import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount, formatTotal } from '../utils/formatters';

interface TradingMetricsProps {
    tradingData: any;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    showCompactView?: boolean;
}

export const TradingMetrics: React.FC<TradingMetricsProps> = ({
                                                                  tradingData,
                                                                  decimalPlaces,
                                                                  showCompactView = false,
                                                              }) => {
    const styles = useStyles2(getStyles);

    if (!tradingData) {
        return null;
    }

    // Extract values from trading data
    const {
        stock,
        money,
        best_buy_market,
        best_sell_market,
        max_buy_price,
        min_sell_price,
        buy_spread,
        sell_spread,
        taker_buy_spread,
        taker_sell_spread,
        max_position,
        min_position,
        ref_order_size_buy,
        ref_order_size_sell,
    } = tradingData;

    const formatPercent = (value: number) => {
        if (value === undefined || value === null) return '-';
        return (value * 100).toFixed(2) + '%';
    };

    // Parse the full market ID to extract exchange and trading pair
    const parseMarket = (fullMarketId: string) => {
        if (!fullMarketId) return { exchange: '', market: '' };
        const parts = fullMarketId.split(':');

        if (parts.length > 1) {
            return {
                exchange: parts[0],
                market: parts[1]
            };
        }

        return {
            exchange: fullMarketId,
            market: ''
        };
    };

    // Use compact view for small panels
    if (showCompactView) {
        return (
            <div className={styles.compactContainer}>
                <div className={styles.compactRow}>
                    {/* Show Base and Quote asset balances separately in compact view */}
                    <div className={styles.compactMetric}>
                        <div className={styles.metricLabel}>{stock.asset}</div>
                        <div className={styles.metricValue}>
                            {formatAmount(stock.total, decimalPlaces.amount)}
                        </div>
                    </div>
                    <div className={styles.compactMetric}>
                        <div className={styles.metricLabel}>{money.asset}</div>
                        <div className={styles.metricValue}>
                            {formatTotal(money.total, decimalPlaces.total)}
                        </div>
                    </div>

                    <div className={styles.compactMetric}>
                        <div className={styles.metricLabel}>Buy/Sell Spread</div>
                        <div className={styles.metricValue}>
                            <span className={styles.buyValue}>{formatPercent(buy_spread)}</span>
                            /
                            <span className={styles.sellValue}>{formatPercent(sell_spread)}</span>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Extract market info for both buy and sell markets
    const buyMarketInfo = parseMarket(best_buy_market);
    const sellMarketInfo = parseMarket(best_sell_market);

    return (
        <div className={styles.container}>
            <div className={styles.section}>
                <div className={styles.sectionHeader}>Bot Status</div>
                <div className={styles.metricsGrid}>
                    {/* Display asset balances separately for better visibility */}
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>{stock.asset}</div>
                        <div className={styles.metricValue}>
                            {formatAmount(stock.total, decimalPlaces.amount)}
                        </div>
                    </div>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>{money.asset}</div>
                        <div className={styles.metricValue}>
                            {formatTotal(money.total, decimalPlaces.total)}
                        </div>
                    </div>
                </div>
            </div>

            <div className={styles.section}>
                <div className={styles.sectionHeader}>Market Best Prices</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.metric}>
                        <div className={cx(styles.metricLabel)}>
                            {buyMarketInfo.exchange}
                        </div>
                        <div className={cx(styles.metricLabel)}>
                            {buyMarketInfo.market}
                        </div>
                        <div className={cx(styles.metricValue, styles.buyValue)}>
                            {formatPrice(max_buy_price, decimalPlaces.price)}
                        </div>
                    </div>

                    <div className={styles.metric}>
                        <div className={cx(styles.metricLabel)}>
                            {sellMarketInfo.exchange}
                        </div>
                        <div className={cx(styles.metricLabel)}>
                            {sellMarketInfo.market}
                        </div>
                        <div className={cx(styles.metricValue, styles.sellValue)}>
                            {formatPrice(min_sell_price, decimalPlaces.price)}
                        </div>
                    </div>
                </div>
            </div>

            <div className={styles.section}>
                <div className={styles.sectionHeader}>Spreads</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.spreadMetric}>
                        <div className={styles.metricLabel}>Buy Spread</div>
                        <div className={cx(styles.metricValue, styles.buyValue)}>
                            {formatPercent(buy_spread)}
                        </div>
                    </div>

                    <div className={styles.spreadMetric}>
                        <div className={styles.metricLabel}>Sell Spread</div>
                        <div className={cx(styles.metricValue, styles.sellValue)}>
                            {formatPercent(sell_spread)}
                        </div>
                    </div>

                    <div className={styles.spreadMetric}>
                        <div className={styles.metricLabel}>Taker Buy</div>
                        <div className={cx(styles.metricValue, styles.buyValue)}>
                            {formatPercent(taker_buy_spread)}
                        </div>
                    </div>

                    <div className={styles.spreadMetric}>
                        <div className={styles.metricLabel}>Taker Sell</div>
                        <div className={cx(styles.metricValue, styles.sellValue)}>
                            {formatPercent(taker_sell_spread)}
                        </div>
                    </div>
                </div>
            </div>

            <div className={styles.section}>
                <div className={styles.sectionHeader}>Position Settings</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Max Position</div>
                        <div className={styles.metricValue}>
                            {formatAmount(max_position, decimalPlaces.amount)}
                        </div>
                    </div>

                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Min Position</div>
                        <div className={styles.metricValue}>
                            {formatAmount(min_position, decimalPlaces.amount)}
                        </div>
                    </div>

                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Order Size Buy</div>
                        <div className={styles.metricValue}>
                            {formatAmount(ref_order_size_buy, decimalPlaces.amount)}
                        </div>
                    </div>

                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Order Size Sell</div>
                        <div className={styles.metricValue}>
                            {formatAmount(ref_order_size_sell, decimalPlaces.amount)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const getStyles = () => {
    return {
        container: css`
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 8px;
      background-color: #121722;
      border-top: 1px solid rgba(255, 255, 255, 0.05);
      overflow-x: auto;
    `,
        section: css`
      flex: 1;
      min-width: 180px;
      background-color: rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      padding: 8px;
      display: flex;
      flex-direction: column;
    `,
        sectionHeader: css`
      font-size: 11px;
      font-weight: 500;
      color: #9096a1;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    `,
        metricsGrid: css`
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 8px;
      flex: 1; /* Take remaining space */
      min-width: 0;
    `,
        metric: css`
      display: flex;
      flex-direction: column;
    `,
        spreadMetric: css`
      display: flex;
      flex-direction: column;
    `,
        metricLabel: css`
      font-size: 10px;
      margin-bottom: 2px;
    `,
        metricValue: css`
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `,
        subValue: css`
      font-size: 10px;
      color: #9096a1;
      margin-left: 4px;
    `,
        buyValue: css`
      color: #00b164;
    `,
        sellValue: css`
      color: #ea0070;
    `,
        compactContainer: css`
      padding: 8px;
      background-color: #121722;
      border-top: 1px solid rgba(255, 255, 255, 0.05);
      overflow-x: auto;
    `,
        compactRow: css`
      display: flex;
      justify-content: flex-start;
      gap: 8px;
      min-width: min-content;
      width: 100%;
    `,
        compactMetric: css`
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      padding: 4px 8px;
      white-space: nowrap;
      flex-shrink: 0;
    `,
    };
};
