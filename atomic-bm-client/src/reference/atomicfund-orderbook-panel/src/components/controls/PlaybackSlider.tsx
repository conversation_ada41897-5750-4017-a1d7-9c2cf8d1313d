import React, { useState, useRef, useCallback, useEffect } from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { dateTime } from '@grafana/data';

interface PlaybackSliderProps {
    timestamps: number[];
    currentIndex: number;
    onSeek: (index: number) => void;
    isPlaying: boolean;
    isMobile?: boolean;
}

export const PlaybackSlider: React.FC<PlaybackSliderProps> = ({
                                                                  timestamps,
                                                                  currentIndex,
                                                                  onSeek,
                                                                  isPlaying,
                                                                  isMobile = false,
                                                              }) => {
    const styles = useStyles2(getStyles);
    const sliderRef = useRef<HTMLDivElement>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [hoverIndex, setHoverIndex] = useState<number | null>(null);
    const [hoverPosition, setHoverPosition] = useState<number>(0);

    // Calculate progress percentage
    const progress = timestamps.length > 0
        ? ((currentIndex + 1) / timestamps.length) * 100
        : 0;

    // Handle mouse down to start dragging
    const handleMouseDown = (e: React.MouseEvent) => {
        if (!sliderRef.current || timestamps.length === 0) return;

        e.preventDefault();
        setIsDragging(true);

        // Calculate and set position immediately on mouse down
        updatePositionFromMouse(e.clientX);
    };

    // Handle touch start for mobile
    const handleTouchStart = (e: React.TouchEvent) => {
        if (!sliderRef.current || timestamps.length === 0) return;

        // Don't prevent default here to allow scrolling if not on slider
        if (e.targetTouches.length > 0) {
            setIsDragging(true);
            updatePositionFromTouch(e.targetTouches[0].clientX);
        }
    };

    // Update position from touch coordinates
    const updatePositionFromTouch = (clientX: number) => {
        if (!sliderRef.current || timestamps.length === 0) return;

        const rect = sliderRef.current.getBoundingClientRect();
        const position = clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, position / rect.width));
        const newIndex = Math.floor(percentage * timestamps.length);

        // Only seek if it's a different index to avoid unnecessary updates
        if (newIndex !== currentIndex && newIndex >= 0 && newIndex < timestamps.length) {
            onSeek(newIndex);
        }
    };

    // Handle document-level mouse move for dragging
    const handleDocumentMouseMove = useCallback((e: MouseEvent) => {
        if (!isDragging || !sliderRef.current) return;
        e.preventDefault();
        updatePositionFromMouse(e.clientX);
    }, [isDragging]);

    // Handle document-level touch move for dragging
    const handleDocumentTouchMove = useCallback((e: TouchEvent) => {
        if (!isDragging || !sliderRef.current) return;

        // Prevent page scrolling while dragging the slider
        e.preventDefault();

        if (e.targetTouches.length > 0) {
            updatePositionFromTouch(e.targetTouches[0].clientX);
        }
    }, [isDragging]);

    // Handle document-level mouse up to stop dragging
    const handleDocumentMouseUp = useCallback((e: MouseEvent) => {
        if (isDragging) {
            e.preventDefault();
        }
        setIsDragging(false);
    }, [isDragging]);

    // Handle document-level touch end to stop dragging
    const handleDocumentTouchEnd = useCallback(() => {
        setIsDragging(false);
    }, []);

    // Set up and clean up document-level event listeners for dragging
    useEffect(() => {
        if (isDragging) {
            document.addEventListener('mousemove', handleDocumentMouseMove);
            document.addEventListener('mouseup', handleDocumentMouseUp);
            document.addEventListener('touchmove', handleDocumentTouchMove, { passive: false });
            document.addEventListener('touchend', handleDocumentTouchEnd);
        }

        return () => {
            document.removeEventListener('mousemove', handleDocumentMouseMove);
            document.removeEventListener('mouseup', handleDocumentMouseUp);
            document.removeEventListener('touchmove', handleDocumentTouchMove);
            document.removeEventListener('touchend', handleDocumentTouchEnd);
        };
    }, [isDragging, handleDocumentMouseMove, handleDocumentMouseUp, handleDocumentTouchMove, handleDocumentTouchEnd]);

    // Update position from mouse coordinates
    const updatePositionFromMouse = (clientX: number) => {
        if (!sliderRef.current || timestamps.length === 0) return;

        const rect = sliderRef.current.getBoundingClientRect();
        const position = clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, position / rect.width));
        const newIndex = Math.floor(percentage * timestamps.length);

        // Only seek if it's a different index to avoid unnecessary updates
        if (newIndex !== currentIndex && newIndex >= 0 && newIndex < timestamps.length) {
            onSeek(newIndex);
        }
    };

    // Handle mouse move over slider for hover effects
    const handleMouseMove = (e: React.MouseEvent) => {
        if (!sliderRef.current || timestamps.length === 0) return;

        const rect = sliderRef.current.getBoundingClientRect();
        const mousePosition = e.clientX - rect.left;
        setHoverPosition(mousePosition);

        const percentage = Math.max(0, Math.min(1, mousePosition / rect.width));
        const index = Math.floor(percentage * timestamps.length);

        if (index >= 0 && index < timestamps.length) {
            setHoverIndex(index);
        }
    };

    // Format timestamp for tooltip
    const formatTimestamp = (timestamp: number) => {
        return dateTime(timestamp).format('HH:mm:ss');
    };

    return (
        <div
            className={styles.sliderContainer}
            onMouseLeave={() => setHoverIndex(null)}
        >
            <div
                ref={sliderRef}
                className={styles.slider}
                onMouseMove={handleMouseMove}
                onMouseDown={handleMouseDown}
                onTouchStart={handleTouchStart}
            >
                <div
                    className={cx(styles.progress, isPlaying && styles.playing)}
                    style={{ width: `${progress}%` }}
                />
                <div
                    className={cx(styles.handle, isDragging && styles.dragging)}
                    style={{ left: `${progress}%` }}
                />

                {/* Timestamp markers */}
                {timestamps.length > 0 && Array.from({ length: isMobile ? 3 : 5 }).map((_, i) => {
                    const markerIndex = Math.floor((i / (isMobile ? 2 : 4)) * (timestamps.length - 1));
                    const position = `${(markerIndex / (timestamps.length - 1)) * 100}%`;
                    return (
                        <div key={i} className={styles.marker} style={{ left: position }}>
                            <div className={styles.markerLine} />
                            <div className={styles.markerLabel}>
                                {formatTimestamp(timestamps[markerIndex])}
                            </div>
                        </div>
                    );
                })}

                {/* Hover tooltip - only show on desktop */}
                {!isMobile && hoverIndex !== null && !isDragging && (
                    <div
                        className={styles.tooltip}
                        style={{ left: `${hoverPosition}px` }}
                    >
                        {formatTimestamp(timestamps[hoverIndex])}
                    </div>
                )}
            </div>
        </div>
    );
};

const getStyles = () => ({
    sliderContainer: css`
        padding: 0px 16px 8px;
        position: relative;
        user-select: none;
    `,
    slider: css`
        height: 6px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        position: relative;
        cursor: pointer;
        touch-action: none;
    `,
    progress: css`
        height: 100%;
        background-color: #5794F2;
        border-radius: 3px;
        position: absolute;
        left: 0;
        top: 0;
    `,
    playing: css`
        background-color: #5794F2;
        background-image: linear-gradient(45deg, 
          rgba(255, 255, 255, 0.1) 25%, 
          transparent 25%, 
          transparent 50%, 
          rgba(255, 255, 255, 0.1) 50%, 
          rgba(255, 255, 255, 0.1) 75%, 
          transparent 75%, 
          transparent);
        background-size: 20px 20px;
        animation: playback-progress 1s linear infinite;
        
        @keyframes playback-progress {
          0% { background-position: 0 0; }
          100% { background-position: 20px 0; }
        }
    `,
    handle: css`
        width: 14px;
        height: 14px;
        background-color: #fff;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        transition: transform 0.1s ease;
        cursor: pointer;
    `,
    dragging: css`
        transform: translate(-50%, -50%) scale(1.2);
        cursor: pointer;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
    `,
    marker: css`
        position: absolute;
        top: 0;
        transform: translateX(-50%);
        height: 6px;
    `,
    markerLine: css`
        width: 1px;
        height: 8px;
        background-color: rgba(255, 255, 255, 0.3);
        position: absolute;
        bottom: 0;
        left: 0;
    `,
    markerLabel: css`
        position: absolute;
        bottom: -18px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
        white-space: nowrap;
    `,
    tooltip: css`
        position: absolute;
        bottom: 20px;
        transform: translateX(-50%);
        background-color: #1f2430;
        color: #fff;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        pointer-events: none;
    `,
});
