// src/components/controls/PlaybackControls.tsx - Update this existing file
import React, { useState } from 'react';
import { css, cx } from '@emotion/css';
import { Button, Icon, useStyles2 } from '@grafana/ui';
import { PlaybackSlider } from './PlaybackSlider';

interface PlaybackControlsProps {
    timestamps: number[];
    isPlaying: boolean;
    currentTimestampIndex: number;
    playbackSpeed: number;
    onPlay: () => void;
    onPause: () => void;
    onSpeedChange: (speed: number) => void;
    onSeek: (index: number) => void;
    isMobile?: boolean;  // Add this new prop
}

export const PlaybackControls: React.FC<PlaybackControlsProps> = ({
                                                                      timestamps,
                                                                      isPlaying,
                                                                      currentTimestampIndex,
                                                                      playbackSpeed,
                                                                      onPlay,
                                                                      onPause,
                                                                      onSpeedChange,
                                                                      onSeek,
                                                                      isMobile = false, // Default to desktop
                                                                  }) => {
    const styles = useStyles2(getStyles);
    const [showSpeedControl, setShowSpeedControl] = useState(false);

    // Updated speed options (multipliers)
    const speedOptions = [1, 2, 5, 10, 25, 50];

    return (
        <div className={cx(styles.container, isMobile && styles.mobileContainer)}>
            <div className={styles.controls}>
                {!isPlaying ? (
                    <Button
                        icon="play"
                        variant="secondary"
                        size="sm"
                        onClick={onPlay}
                        className={cx(styles.button, isMobile && styles.mobileButton)}
                        tooltip="Play orderbook animation"
                    >
                    </Button>
                ) : (
                    <Button
                        icon="pause"
                        variant="secondary"
                        size="sm"
                        onClick={onPause}
                        className={cx(styles.button, isMobile && styles.mobileButton)}
                        tooltip="Pause animation"
                    >
                    </Button>
                )}
            </div>

            {timestamps.length > 0 && (
                <div className={styles.sliderContainer}>
                    <PlaybackSlider
                        timestamps={timestamps}
                        currentIndex={currentTimestampIndex}
                        onSeek={onSeek}
                        isPlaying={isPlaying}
                        isMobile={isMobile}
                    />
                </div>
            )}

            <div className={styles.speedControl}>
                <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => setShowSpeedControl(!showSpeedControl)}
                    className={cx(styles.speedButton, isMobile && styles.mobileSpeedButton)}
                    tooltip="Playback speed"
                >
                    <Icon name="clock-nine" /> {playbackSpeed}x
                </Button>

                {showSpeedControl && (
                    <div className={cx(styles.speedOptions, isMobile && styles.mobileSpeedOptions)}>
                        {speedOptions.map((speed) => (
                            <Button
                                key={speed}
                                variant={playbackSpeed === speed ? "primary" : "secondary"}
                                size="sm"
                                onClick={() => {
                                    onSpeedChange(speed);
                                    setShowSpeedControl(false);
                                }}
                                className={styles.speedOptionButton}
                            >
                                {speed}x
                            </Button>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

const getStyles = () => {
    return {
        container: css`
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background-color: #121722;
      border-top: 1px solid rgba(255, 255, 255, 0.05);
    `,
        mobileContainer: css`
      padding: 8px 12px;
    `,
        controls: css`
      display: flex;
      gap: 8px;
    `,
        button: css`
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
    `,
        mobileButton: css`
      min-width: 36px;
      height: 36px;
      padding: 0;
      border-radius: 50%;
      background-color: rgba(87, 148, 242, 0.2);
      
      /* Fix the icon alignment */
      & > span {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      /* Make the icon slightly larger */
      & svg {
        width: 20px;
        height: 20px;
      }
    `,
        sliderContainer: css`
      flex: 1;
      margin: 0 10px;
      min-width: 150px;
    `,
        speedControl: css`
      position: relative;
    `,
        speedButton: css`
      display: flex;
      align-items: center;
      gap: 4px;
    `,
        mobileSpeedButton: css`
      padding: 4px 8px;
      min-height: 32px;
      font-size: 12px;
    `,
        speedOptions: css`
      position: absolute;
      bottom: 40px;
      right: 0;
      background-color: #1f2430;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.1);
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 8px;
    `,
        mobileSpeedOptions: css`
      bottom: 36px;
      right: -15px;
    `,
        speedOptionButton: css`
      width: 60px;
    `,
    };
};
