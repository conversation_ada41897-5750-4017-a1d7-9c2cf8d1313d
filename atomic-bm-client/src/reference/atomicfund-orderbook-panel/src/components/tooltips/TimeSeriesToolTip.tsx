import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { DataFrame, dateTime } from '@grafana/data';
import { formatPrice } from '../../utils/formatters';

interface TimeSeriesToolTipProps {
    alignedFrame: DataFrame;
    seriesIdx: number | null;
    dataIdx: number | null;
    timeZone: string;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    onSnapshotSelect?: (timestamp: number) => void;
}

export const TimeSeriesToolTip: React.FC<TimeSeriesToolTipProps> = ({
    alignedFrame,
    seriesIdx,
    dataIdx,
    decimalPlaces,
}) => {
    const styles = useStyles2(getStyles);

    if (!alignedFrame || seriesIdx === null || dataIdx === null) {
        return null;
    }

    const timestamp = alignedFrame.fields[0].values[dataIdx];
    const midPrice = alignedFrame.fields[1]?.values[dataIdx];

    return (
        <div className={styles.tooltipContainer}>
            <div className={styles.tooltipRow}>
                <span>Time: {dateTime(timestamp).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
            <div className={styles.tooltipRow}>
                <span>Mid Price: {midPrice !== null ? formatPrice(midPrice, decimalPlaces.price) : '-'}</span>
            </div>
        </div>
    );
};

const getStyles = () => ({
    tooltipContainer: css`
    padding: 8px;
    background: #1f2430;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #ffffff;
  `,
    tooltipRow: css`
    margin-bottom: 4px;
  `,
    viewSnapshotButton: css`
    margin-top: 8px;
    width: 100%;
  `,
});
