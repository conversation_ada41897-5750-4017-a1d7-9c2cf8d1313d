import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount, formatTotal } from '../../utils/formatters';

interface OrderTooltipProps {
    visible: boolean;
    x: number;
    y: number;
    avgPrice: number;
    sumBaseAmount: number;
    sumQuoteValue: number;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    onViewDetails?: () => void;
}

export const OrderTooltip: React.FC<OrderTooltipProps> = ({
                                                              visible,
                                                              x,
                                                              y,
                                                              avgPrice,
                                                              sumBaseAmount,
                                                              sumQuoteValue,
                                                              decimalPlaces,
                                                              onViewDetails,
                                                          }) => {
    const styles = useStyles2(getStyles);

    if (!visible) {
        return null;
    }

    return (
        <div
            className={styles.tooltip}
            style={{
                left: x,
                top: y,
            }}
        >
            <div className={styles.tooltipRow}>
                <div className={styles.tooltipLabel}>Avg. Price:</div>
                <div className={styles.tooltipValue}>
                    ≈ {formatPrice(avgPrice, decimalPlaces.price)}
                </div>
            </div>
            <div className={styles.tooltipRow}>
                <div className={styles.tooltipLabel}>Sum Base:</div>
                <div className={styles.tooltipValue}>
                    {formatAmount(sumBaseAmount, decimalPlaces.amount)}
                </div>
            </div>
            <div className={styles.tooltipRow}>
                <div className={styles.tooltipLabel}>Sum Quote:</div>
                <div className={styles.tooltipValue}>
                    {formatTotal(sumQuoteValue, decimalPlaces.total)}
                </div>
            </div>
        </div>
    );
};

const getStyles = () => ({
    tooltip: css`
    position: absolute;
    background: #1f2430;
    border-radius: 4px;
    padding: 10px 12px;
    font-size: 12px;
    z-index: 999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    pointer-events: auto;
    max-width: 220px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 200px;
    animation: fadeIn 0.2s ease-out;
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(5px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `,
    tooltipRow: css`
    margin-bottom: 6px;
    color: #ffffff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    white-space: nowrap;
  `,
    tooltipLabel: css`
    color: #9096a1;
    margin-right: 8px;
  `,
    tooltipValue: css`
    font-weight: 500;
    text-align: right;
  `,
    divider: css`
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 8px 0;
  `,
    actionButton: css`
    width: 100%;
    padding: 6px 0;
    background: rgba(87, 148, 242, 0.2);
    border: 1px solid #5794F2;
    border-radius: 4px;
    color: #5794F2;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 4px;
    
    &:hover {
      background: rgba(87, 148, 242, 0.3);
    }
    
    &:active {
      background: rgba(87, 148, 242, 0.4);
      transform: translateY(1px);
    }
  `,
});
