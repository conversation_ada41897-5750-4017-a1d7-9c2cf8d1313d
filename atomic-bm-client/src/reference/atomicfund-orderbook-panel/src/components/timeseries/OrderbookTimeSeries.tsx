import React from 'react';
import { css } from '@emotion/css';
import {useStyles2, TimeSeries, TooltipPlugin, VizLegendOptions, LegendDisplayMode} from '@grafana/ui';
import { DataFrame, TimeRange, Field, FieldType } from '@grafana/data';
import { TooltipDisplayMode } from '@grafana/schema';
import { ClickPlugin } from '../ClickPlugin';
import { formatPrice } from '../../utils/formatters';

interface OrderbookTimeSeriesProps {
    frames: DataFrame[];
    timeRange: TimeRange;
    timeZone: string;
    width: number;
    height: number;
    onSnapshotSelect: (timestamp: number) => void;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
}

export const OrderbookTimeSeries: React.FC<OrderbookTimeSeriesProps> = ({
    frames,
    timeRange,
    timeZone,
    width,
    height,
    onSnapshotSelect,
    decimalPlaces,
}) => {
    const styles = useStyles2(getStyles);

    if (frames.length === 0) {
        return null;
    }

    // Find appropriate min and max for y-axis to ensure visibility
    let minPrice = Number.MAX_VALUE;
    let maxPrice = Number.MIN_VALUE;

    frames.forEach(frame => {
        const priceField = frame.fields.find(f => f.name === 'price' || f.name === 'midPrice');
        if (priceField && priceField.values && priceField.values.length > 0) {
            // Iterate through price values to find min and max
            for (let i = 0; i < priceField.values.length; i++) {
                const price = priceField.values[i];
                if (price !== null && !isNaN(price)) {
                    minPrice = Math.min(minPrice, price);
                    maxPrice = Math.max(maxPrice, price);
                }
            }
        }
    });

    // Add padding to min and max for better visibility (0.1% of range)
    if (minPrice !== Number.MAX_VALUE && maxPrice !== Number.MIN_VALUE) {
        const range = maxPrice - minPrice;
        if (range === 0) {
            // If all values are the same, add a small range around it
            minPrice = minPrice * 0.9999;
            maxPrice = maxPrice * 1.0001;
        } else {
            // Add padding based on range
            const padding = range * 0.001;
            minPrice -= padding;
            maxPrice += padding;
        }
    }

    // Create proper legend options
    const legendOptions: VizLegendOptions = {
        asTable: false,
        isVisible: false,
        showLegend: false,
        sortBy: "",
        sortDesc: false,
        width: 0,
        displayMode: LegendDisplayMode.Hidden,
        placement: 'bottom',
        calcs: []
    };

    return (
        <div className={styles.timeSeriesContainer} style={{ height }}>
            <TimeSeries
                frames={frames}
                timeRange={timeRange}
                timeZone={timeZone}
                width={width}
                height={height}
                legend={legendOptions}
                tweakScale={(opts: any, forField: Field) => {
                    // Only apply to y-axis (non-time fields)
                    if (forField.type !== FieldType.time &&
                        (forField.name === 'price' || forField.name === 'midPrice')) {
                        if (typeof opts === 'object' && opts !== null) {
                            // Set custom min and max to focus on the price range
                            opts.min = minPrice;
                            opts.max = maxPrice;
                        }
                    }
                    return opts;
                }}
                tweakAxis={(opts: any, forField: Field) => {
                    // Only apply to y-axis (non-time fields)
                    if (forField.type !== FieldType.time) {
                        if (typeof opts === 'boolean') {
                            return opts;
                        }

                        if (typeof opts === 'object' && opts !== null) {
                            opts.label = forField.name === 'midPrice' ? 'Mid Price' :
                                forField.name === 'price' ? 'Price' : forField.name;

                            opts.formatValue = (value: number | null) => {
                                if (value === null || value === undefined) {
                                    return '-';
                                }
                                return formatPrice(value, decimalPlaces.price);
                            };

                            if (typeof opts.show === 'undefined') {
                                opts.show = true;
                            }

                            if (typeof opts.grid === 'undefined') {
                                opts.grid = true;
                            }
                        }
                    }
                    return opts;
                }}
            >
                {(config, alignedDataFrame) => (
                    <>
                        <TooltipPlugin
                            mode={TooltipDisplayMode.Single}
                            config={config}
                            data={alignedDataFrame || []}
                            timeZone={timeZone}
                        />
                        <ClickPlugin
                            config={config}
                            onClickPoint={onSnapshotSelect}
                        />
                    </>
                )}
            </TimeSeries>
        </div>
    );
};

const getStyles = () => ({
    timeSeriesContainer: css`
        margin-bottom: 5px;
        margin-top: 5px;
        padding-left: 5px;
        position: relative;
        max-width: 100%;
        overflow: hidden;
    `,
});
