import React, { useState, useMemo } from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount } from '../utils/formatters';

// Position type definition
interface Position {
    amount: number;
    closing_err_at: string;
    closing_order_id: string;
    id: number;
    left_amount: number;
    opened_at: string;
    order_id: string;
    prev_price: number;
    price: number;
    side: 'bid' | 'ask';
    state: string;
}

// Order type definition (for bids and asks)
interface Order {
    amount: number;
    has_open_orders: boolean;
    price: number;
    ref_spread: number;
    side: string;
    top_spread: number;
}

// Trading data type definition
interface TradingData {
    positions: Position[];
    bids?: Order[];
    asks?: Order[];
    max_position?: number;
    stock?: {
        asset: string;
    };
    money?: {
        asset: string;
    };
    [key: string]: any; // Allow for additional properties that might be in the trading data
}

// Position with spread info
interface EnhancedPosition extends Position {
    ref_spread?: number;
    top_spread?: number;
}

// Component props updated to include ts
interface PositionsManagementPanelProps {
    tradingData: TradingData;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    currentPrice: number;
    ts: number; // The timestamp from the query
}

export const PositionsManagementPanel: React.FC<PositionsManagementPanelProps> = ({
                                                                                      tradingData,
                                                                                      decimalPlaces,
                                                                                      currentPrice,
                                                                                      ts,
                                                                                  }) => {
    const styles = useStyles2(getStyles);
    const [copiedOrderId, setCopiedOrderId] = useState<string | null>(null);

    // Check if we have positions data
    if (!tradingData || !tradingData.positions || tradingData.positions.length === 0) {
        return (
            <div className={styles.container}>
                <div className={styles.header}>
                    <span>OPEN ORDERS</span>
                </div>
                <div className={styles.noData}>No orders.</div>
            </div>
        );
    }

    // Enhance positions with spread information from bids and asks
    const enhancedPositions = useMemo(() => {
        // Ensure we have the needed data
        if (!tradingData.positions) return [];

        // Define a helper function to find the best matching order
        const findMatchingOrder = (position: Position): Order | undefined => {
            const orders = position.side === 'bid' ? tradingData.bids : tradingData.asks;

            if (!orders || orders.length === 0) {
                return undefined;
            }

            // First try to find an exact price match with has_open_orders=true
            let match = orders.find(order =>
                Math.abs(order.price - position.price) < 0.000001 && order.has_open_orders
            );

            // If no exact match found, try to find the closest price with has_open_orders=true
            if (!match) {
                const ordersWithOpenOrders = orders.filter(order => order.has_open_orders);
                if (ordersWithOpenOrders.length > 0) {
                    // Sort by the absolute difference in price
                    ordersWithOpenOrders.sort((a, b) =>
                        Math.abs(a.price - position.price) - Math.abs(b.price - position.price)
                    );
                    match = ordersWithOpenOrders[0];
                }
            }

            // If still no match, just use the closest price regardless of has_open_orders
            if (!match) {
                // Sort all orders by the absolute difference in price
                const sortedOrders = [...orders].sort((a, b) =>
                    Math.abs(a.price - position.price) - Math.abs(b.price - position.price)
                );
                match = sortedOrders[0];
            }

            return match;
        };

        return tradingData.positions.map(position => {
            const enhancedPosition: EnhancedPosition = { ...position };
            const matchingOrder = findMatchingOrder(position);

            if (matchingOrder) {
                enhancedPosition.ref_spread = matchingOrder.ref_spread;
                enhancedPosition.top_spread = matchingOrder.top_spread;
            }

            return enhancedPosition;
        });
    }, [tradingData]);

    // Handle copy to clipboard
    const handleCopyOrderId = (orderId: string) => {
        if (!orderId) return;

        navigator.clipboard.writeText(orderId)
            .then(() => {
                setCopiedOrderId(orderId);
                setTimeout(() => setCopiedOrderId(null), 1500);
            })
            .catch(err => {
                console.error('Failed to copy order ID:', err);
            });
    };

    // Filter positions into active (has open orders) and desired (unused/pending)
    const activePositions: EnhancedPosition[] = [];
    const desiredPositions: EnhancedPosition[] = [];

    // Helper function to check if a position is active by looking at order state
    const isPositionActive = (position: EnhancedPosition): boolean => {
        // Position is active if it has state "open" or "active"
        return position.state === "open" || position.state === "active";
    };

    // Helper function to truncate order ID for display
    const truncateOrderId = (orderId: string): string => {
        if (!orderId) return 'N/D';
        if (orderId.length <= 8) return orderId;
        return `${orderId.substring(0, 4)}...${orderId.substring(orderId.length - 3)}`;
    };

    // Categorize positions
    enhancedPositions.forEach((position: EnhancedPosition) => {
        if (isPositionActive(position)) {
            activePositions.push(position);
        } else {
            desiredPositions.push(position);
        }
    });

    // Check if a timestamp is valid
    const isValidTimestamp = (timestamp: string): boolean => {
        if (!timestamp) return false;

        // Check for "0001-01-01" which is often used as a zero date in Go/databases
        if (timestamp.startsWith('0001-01-01')) return false;

        const date = new Date(timestamp);
        // Check if date is valid and not in the distant past (1970 is UNIX epoch)
        return !isNaN(date.getTime()) && date.getFullYear() > 1970;
    };

    // Calculate position age in a readable format based on data timestamp
    const calculateAge = (timestamp: string) => {
        if (!isValidTimestamp(timestamp)) {
            return 'N/D';
        }

        // Use ts directly as the reference time
        const referenceTime = ts;

        const openTime = new Date(timestamp).getTime();
        const diffMs = referenceTime - openTime;

        // Convert to seconds
        const diffSec = Math.floor(diffMs / 1000);

        // Handle negative time (shouldn't happen in normal scenarios, but just in case)
        if (diffSec < 0) return '0s';

        if (diffSec < 60) return `${diffSec}s`;

        // Convert to minutes
        const diffMin = Math.floor(diffSec / 60);
        const remainingSec = diffSec % 60;

        if (diffMin < 60) return `${diffMin}m ${remainingSec}s`;

        // Convert to hours
        const diffHours = Math.floor(diffMin / 60);
        const remainingMin = diffMin % 60;

        if (diffHours < 24) return `${diffHours}h ${remainingMin}m`;

        // Convert to days
        const diffDays = Math.floor(diffHours / 24);
        const remainingHours = diffHours % 24;

        return `${diffDays}d ${remainingHours}h`;
    };

    // Format timestamp or return N/D if invalid
    const formatTimestamp = (timestamp: string) => {
        if (!isValidTimestamp(timestamp)) {
            return 'N/D';
        }
        return new Date(timestamp).toLocaleTimeString();
    };

    // Format spread as percentage
    const formatSpread = (spread?: number) => {
        if (spread === undefined || spread === null) return '-';
        return (spread * 100).toFixed(3) + '%';
    };

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <span>OPEN ORDERS</span>
            </div>

            {/* Table Structure */}
            <div className={styles.tableWrapper}>
                {/* Table Header */}
                <table className={styles.table}>
                    <thead>
                    <tr className={styles.headerRow}>
                        <th className={styles.colOrderId}>OrderID</th>
                        <th className={styles.colSide}>Side</th>
                        <th className={styles.colSize}>Size</th>
                        <th className={styles.colSize}>Left</th>
                        <th className={styles.colPrice}>Entry</th>
                        <th className={styles.colPrice}>Current</th>
                        <th className={styles.colSpread}>Ref %</th>
                        <th className={styles.colSpread}>Top %</th>
                        <th className={styles.colStatus}>Status</th>
                        <th className={styles.colTime}>Open Time</th>
                        <th className={styles.colAge}>Age</th>
                    </tr>
                    </thead>
                    {/* Active Positions */}
                    <tbody>
                    {activePositions.length > 0 ? (
                        activePositions.map((position, index) => (
                            <tr
                                key={`active-${position.id}-${index}`}
                                className={`${styles.dataRow} ${index % 2 === 0 ? styles.evenRow : ''} ${styles.activeRow}`}
                            >
                                {/* Order ID with copy functionality */}
                                <td className={styles.colOrderId}>
                                    {position.order_id ? (
                                        <div
                                            className={`${styles.orderIdWrapper} ${copiedOrderId === position.order_id ? styles.justCopied : ''}`}
                                            onClick={() => handleCopyOrderId(position.order_id)}
                                            title={`${position.order_id}${copiedOrderId === position.order_id ? ' (Copied!)' : ' (Click to copy)'}`}
                                        >
                                            {truncateOrderId(position.order_id)}
                                            {copiedOrderId === position.order_id ? (
                                                <span className={styles.copiedIndicator}>✓</span>
                                            ) : (
                                                <span className={styles.copyIcon}>📋</span>
                                            )}
                                        </div>
                                    ) : (
                                        <span className={styles.noOrderId}>N/D</span>
                                    )}
                                </td>

                                {/* Side (BID/ASK) */}
                                <td className={styles.colSide}>
                                        <span className={position.side === 'bid' ? styles.bidBadge : styles.askBadge}>
                                            {position.side === 'bid' ? 'BID' : 'ASK'}
                                        </span>
                                </td>

                                {/* Size */}
                                <td className={styles.colSize}>
                                    {formatAmount(position.amount, decimalPlaces.amount)}
                                </td>

                                {/* After the Size column */}
                                <td className={styles.colSize}>
                                    {formatAmount(position.left_amount, decimalPlaces.amount)}
                                </td>

                                {/* Entry Price */}
                                <td className={styles.colPrice}>
                                    {formatPrice(position.price, decimalPlaces.price)}
                                </td>

                                {/* Current Price */}
                                <td className={styles.colPrice}>
                                    {formatPrice(currentPrice, decimalPlaces.price)}
                                </td>

                                {/* Ref Spread */}
                                <td className={styles.colSpread}>
                                    {formatSpread(position.ref_spread)}
                                </td>

                                {/* Top Spread */}
                                <td className={styles.colSpread}>
                                    {formatSpread(position.top_spread)}
                                </td>

                                {/* Status */}
                                <td className={styles.colStatus}>
                                    <span className={styles.statusActive}>Active</span>
                                </td>

                                {/* Open Time */}
                                <td className={styles.colTime} title={isValidTimestamp(position.opened_at) ? position.opened_at : 'Not Available'}>
                                    {formatTimestamp(position.opened_at)}
                                </td>

                                {/* Age */}
                                <td className={styles.colAge}>
                                    {calculateAge(position.opened_at)}
                                </td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan={10} className={styles.noActivePositions}>No active positions</td>
                        </tr>
                    )}

                    {/* Desired Positions */}
                    {desiredPositions.length > 0 && (
                        desiredPositions.map((position, index) => (
                            <tr
                                key={`desired-${position.id}-${index}`}
                                className={`${styles.dataRow} ${index % 2 === 0 ? styles.evenRow : ''} ${styles.desiredRow}`}
                            >
                                {/* Order ID with copy functionality */}
                                <td className={styles.colOrderId}>
                                    {position.order_id ? (
                                        <div
                                            className={`${styles.orderIdWrapper} ${copiedOrderId === position.order_id ? styles.justCopied : ''}`}
                                            onClick={() => handleCopyOrderId(position.order_id)}
                                            title={`${position.order_id}${copiedOrderId === position.order_id ? ' (Copied!)' : ' (Click to copy)'}`}
                                        >
                                            {truncateOrderId(position.order_id)}
                                            {copiedOrderId === position.order_id ? (
                                                <span className={styles.copiedIndicator}>✓</span>
                                            ) : (
                                                <span className={styles.copyIcon}>📋</span>
                                            )}
                                        </div>
                                    ) : (
                                        <span className={styles.noOrderId}>N/D</span>
                                    )}
                                </td>

                                {/* Side (BID/ASK) */}
                                <td className={styles.colSide}>
                                        <span className={position.side === 'bid' ? styles.bidBadge : styles.askBadge}>
                                            {position.side === 'bid' ? 'BID' : 'ASK'}
                                        </span>
                                </td>

                                {/* Size */}
                                <td className={styles.colSize}>
                                    {formatAmount(position.amount, decimalPlaces.amount)}
                                </td>

                                {/* Entry Price */}
                                <td className={styles.colPrice}>
                                    {formatPrice(position.price, decimalPlaces.price)}
                                </td>

                                {/* Current Price */}
                                <td className={styles.colPrice}>
                                    {formatPrice(currentPrice, decimalPlaces.price)}
                                </td>

                                {/* Ref Spread */}
                                <td className={styles.colSpread}>
                                    {formatSpread(position.ref_spread)}
                                </td>

                                {/* Top Spread */}
                                <td className={styles.colSpread}>
                                    {formatSpread(position.top_spread)}
                                </td>

                                {/* Status */}
                                <td className={styles.colStatus}>
                                    <span className={styles.statusDesired}>Desired</span>
                                </td>

                                {/* Open Time */}
                                <td className={styles.colTime} title={isValidTimestamp(position.opened_at) ? position.opened_at : 'Not Available'}>
                                    {formatTimestamp(position.opened_at)}
                                </td>

                                {/* Age */}
                                <td className={styles.colAge}>
                                    {calculateAge(position.opened_at)}
                                </td>
                            </tr>
                        ))
                    )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const getStyles = () => {
    return {
        container: css`
            margin: 10px;
            background-color: #121722;
            border-radius: 4px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            flex: 1;
            min-height: 0;
        `,
        header: css`
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 16px;
            background-color: #1a202c;
            color: #9096a1;
            font-size: 12px;
            font-weight: 500;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            flex-shrink: 0; // Prevent header from shrinking
        `,
        tableWrapper: css`
            overflow-y: auto;
            flex: 1; // Allow table to take remaining space
            min-height: 0; // Critical for scroll to work in flexbox!
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        `,
        table: css`
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        `,
        summaryGrid: css`
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            padding: 12px 16px;
            gap: 16px;
            background-color: rgba(26, 32, 44, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        `,
        summaryItem: css`
            display: flex;
            flex-direction: column;
        `,
        summaryLabel: css`
            color: #9096a1;
            font-size: 11px;
            margin-bottom: 4px;
            white-space: nowrap;
        `,
        summaryValue: css`
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        `,
        headerRow: css`
            position: sticky;
            top: 0;
            background-color: #1a202c;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #9096a1;
            font-weight: 500;
            font-size: 11px;
            z-index: 2;
            
            & th {
                padding: 8px 10px;
                text-align: left;
                white-space: nowrap;
                position: relative;
                
                &:after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 25%;
                    height: 50%;
                    width: 1px;
                    background-color: rgba(255, 255, 255, 0.05);
                }
                
                &:last-child:after {
                    display: none;
                }
            }
        `,
        dataRow: css`
            border-bottom: 1px solid rgba(255, 255, 255, 0.03);
            transition: background-color 0.2s;
            
            &:hover {
                background-color: rgba(87, 148, 242, 0.05) !important;
            }
            
            & td {
                padding: 6px 10px;
                vertical-align: middle;
            }
        `,
        activeRow: css`
            // Specific styling for active rows
        `,
        desiredRow: css`
            opacity: 0.75;
            background-color: rgba(26, 32, 44, 0.15);
        `,
        evenRow: css`
            background-color: rgba(26, 32, 44, 0.3);
        `,
        colOrderId: css`
            width: 90px;
        `,
        colSide: css`
            width: 50px;
            text-align: center;
        `,
        colSize: css`
            width: 80px;
        `,
        colPrice: css`
            width: 80px;
        `,
        colSpread: css`
            width: 70px;
            text-align: center;
        `,
        colStatus: css`
            width: 70px;
            text-align: center;
        `,
        colTime: css`
            width: 80px;
        `,
        colAge: css`
            width: 70px;
        `,
        orderIdWrapper: css`
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-family: monospace;
            font-size: 11px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            padding: 2px 8px;
            color: #9096a1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
            cursor: pointer;
            transition: background-color 0.2s;
            
            &:hover {
                background-color: rgba(255, 255, 255, 0.2);
                
                .copyIcon {
                    visibility: visible;
                    opacity: 0.8;
                }
            }
        `,
        justCopied: css`
            background-color: rgba(0, 177, 100, 0.2) !important;
            color: #00b164;
            animation: flash 0.5s;
            
            @keyframes flash {
                0% { background-color: rgba(0, 177, 100, 0.5); }
                100% { background-color: rgba(0, 177, 100, 0.2); }
            }
        `,
        noOrderId: css`
            color: #9096a1;
            font-size: 11px;
            opacity: 0.7;
        `,
        copiedIndicator: css`
            font-size: 10px;
            color: #00b164;
        `,
        copyIcon: css`
            font-size: 10px;
            opacity: 0.6;
            visibility: hidden;
        `,
        bidBadge: css`
            display: inline-block;
            background-color: rgba(0, 177, 100, 0.2);
            color: #00b164;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
            font-size: 11px;
            text-align: center;
            min-width: 36px;
        `,
        askBadge: css`
            display: inline-block;
            background-color: rgba(234, 0, 112, 0.2);
            color: #ea0070;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
            font-size: 11px;
            text-align: center;
            min-width: 36px;
        `,
        statusActive: css`
            display: inline-block;
            background-color: rgba(0, 177, 100, 0.2);
            color: #00b164;
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: 500;
            min-width: 50px;
        `,
        statusDesired: css`
            display: inline-block;
            background-color: rgba(87, 148, 242, 0.2);
            color: #5794F2;
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: 500;
            min-width: 50px;
        `,
        noActivePositions: css`
            padding: 20px;
            text-align: center;
            color: #9096a1;
            font-style: italic;
            font-size: 13px;
        `,
        noData: css`
            padding: 30px;
            text-align: center;
            color: #9096a1;
            font-size: 14px;
        `,
    };
};
