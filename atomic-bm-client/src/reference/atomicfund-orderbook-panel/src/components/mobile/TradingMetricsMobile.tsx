import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount, formatTotal } from '../../utils/formatters';

interface TradingMetricsMobileProps {
    tradingData: any;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
}

export const TradingMetricsMobile: React.FC<TradingMetricsMobileProps> = ({
                                                                              tradingData,
                                                                              decimalPlaces,
                                                                          }) => {
    const styles = useStyles2(getStyles);

    if (!tradingData) {
        return (
            <div className={styles.container}>
                <div className={styles.noData}>No trading metrics available</div>
            </div>
        );
    }

    // Helper for formatting percentages
    const formatPercent = (value: number) => {
        if (value === undefined || value === null) return '-';
        return (value * 100).toFixed(2) + '%';
    };

    // Parse market information
    const parseMarket = (fullMarketId: string) => {
        if (!fullMarketId) return { exchange: '', market: '' };
        const parts = fullMarketId.split(':');
        return {
            exchange: parts[0] || '',
            market: parts.length > 1 ? parts[1] : ''
        };
    };

    // Extract key data
    const { stock, money, best_buy_market, best_sell_market, max_buy_price, min_sell_price,
        buy_spread, sell_spread, max_position, min_position } = tradingData;

    const buyMarketInfo = parseMarket(best_buy_market);
    const sellMarketInfo = parseMarket(best_sell_market);

    return (
        <div className={styles.container}>
            {/* Assets and Balances Section */}
            <div className={styles.section}>
                <div className={styles.sectionHeader}>BALANCES</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>{stock?.asset || 'Base'}</div>
                        <div className={styles.metricValue}>
                            {formatAmount(stock?.total || 0, decimalPlaces.amount)}
                        </div>
                    </div>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>{money?.asset || 'Quote'}</div>
                        <div className={styles.metricValue}>
                            {formatTotal(money?.total || 0, decimalPlaces.total)}
                        </div>
                    </div>
                </div>
            </div>

            {/* Market Best Prices */}
            <div className={styles.section}>
                <div className={styles.sectionHeader}>MARKET BEST PRICES</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>
                            {buyMarketInfo.exchange} {buyMarketInfo.market}
                        </div>
                        <div className={cx(styles.metricValue, styles.buyValue)}>
                            {formatPrice(max_buy_price || 0, decimalPlaces.price)}
                        </div>
                    </div>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>
                            {sellMarketInfo.exchange} {sellMarketInfo.market}
                        </div>
                        <div className={cx(styles.metricValue, styles.sellValue)}>
                            {formatPrice(min_sell_price || 0, decimalPlaces.price)}
                        </div>
                    </div>
                </div>
            </div>

            {/* Spreads */}
            <div className={styles.section}>
                <div className={styles.sectionHeader}>SPREADS</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Buy Spread</div>
                        <div className={cx(styles.metricValue, styles.buyValue)}>
                            {formatPercent(buy_spread || 0)}
                        </div>
                    </div>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Sell Spread</div>
                        <div className={cx(styles.metricValue, styles.sellValue)}>
                            {formatPercent(sell_spread || 0)}
                        </div>
                    </div>
                </div>
            </div>

            {/* Position Settings */}
            <div className={styles.section}>
                <div className={styles.sectionHeader}>POSITION SETTINGS</div>
                <div className={styles.metricsGrid}>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Max Position</div>
                        <div className={styles.metricValue}>
                            {formatAmount(max_position || 0, decimalPlaces.amount)}
                        </div>
                    </div>
                    <div className={styles.metric}>
                        <div className={styles.metricLabel}>Min Position</div>
                        <div className={styles.metricValue}>
                            {formatAmount(min_position || 0, decimalPlaces.amount)}
                        </div>
                    </div>
                </div>
            </div>

            {/* Reference Markets (if available) */}
            {tradingData.ref_markets && tradingData.ref_markets.length > 0 && (
                <div className={styles.section}>
                    <div className={styles.sectionHeader}>REFERENCE MARKETS</div>
                    <div className={styles.scrollableTable}>
                        <table className={styles.table}>
                            <thead>
                            <tr>
                                <th>Exchange</th>
                                <th>Buy Price</th>
                                <th>Sell Price</th>
                                <th>Spread</th>
                            </tr>
                            </thead>
                            <tbody>
                            {tradingData.ref_markets.slice(0, 5).map((market: any, index: number) => {
                                const { exchange } = parseMarket(market.market_id);
                                const spread = market.sell_price && market.buy_price
                                    ? ((market.sell_price - market.buy_price) / market.buy_price) * 100
                                    : 0;

                                return (
                                    <tr key={`market-${index}`}>
                                        <td>{exchange}</td>
                                        <td className={styles.buyValue}>
                                            {formatPrice(market.buy_price || 0, decimalPlaces.price)}
                                        </td>
                                        <td className={styles.sellValue}>
                                            {formatPrice(market.sell_price || 0, decimalPlaces.price)}
                                        </td>
                                        <td>{spread.toFixed(3)}%</td>
                                    </tr>
                                );
                            })}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}
        </div>
    );
};

const getStyles = () => ({
    container: css`
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    overflow-y: auto;
    height: 100%;
  `,
    section: css`
    background-color: rgba(18, 23, 34, 0.6);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
  `,
    sectionHeader: css`
    padding: 8px 12px;
    background-color: rgba(26, 32, 44, 0.7);
    font-size: 11px;
    font-weight: 500;
    color: #9096a1;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  `,
    metricsGrid: css`
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 8px;
    gap: 10px;
  `,
    metric: css`
    display: flex;
    flex-direction: column;
  `,
    metricLabel: css`
    font-size: 11px;
    color: #9096a1;
    margin-bottom: 2px;
  `,
    metricValue: css`
    font-size: 14px;
    font-weight: 500;
  `,
    buyValue: css`
    color: #00b164;
  `,
    sellValue: css`
    color: #ea0070;
  `,
    scrollableTable: css`
    overflow-x: auto;
    max-height: 180px;
    overflow-y: auto;
  `,
    table: css`
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    
    & th, & td {
      padding: 6px 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      text-align: left;
    }
    
    & th {
      font-weight: 500;
      color: #9096a1;
      font-size: 11px;
      background-color: rgba(26, 32, 44, 0.5);
    }
    
    & tr:nth-child(even) {
      background-color: rgba(26, 32, 44, 0.3);
    }
  `,
    noData: css`
    padding: 20px;
    text-align: center;
    color: #9096a1;
  `,
});
