import React, { useState } from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount } from '../../utils/formatters';

// Define a Position type to prevent the 'any' type warnings
interface Position {
    id: number | string;
    state: string;
    side: 'bid' | 'ask';
    order_id: string;
    amount: number;
    left_amount?: number;
    price: number;
    ref_spread?: number;
    top_spread?: number;
    opened_at: string;
}

interface OrdersTableMobileProps {
    tradingData: any;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
    currentPrice: number;
    ts: number;
}

export const OrdersTableMobile: React.FC<OrdersTableMobileProps> = ({
                                                                        tradingData,
                                                                        decimalPlaces,
                                                                        currentPrice,
                                                                        ts,
                                                                    }) => {
    const styles = useStyles2(getStyles);
    const [copiedOrderId, setCopiedOrderId] = useState<string | null>(null);

    if (!tradingData || !tradingData.positions || tradingData.positions.length === 0) {
        return (
            <div className={styles.container}>
                <div className={styles.noData}>No open orders available</div>
            </div>
        );
    }

    // Helper function to check if a position is active
    const isPositionActive = (position: Position): boolean => {
        return position.state === "open" || position.state === "active";
    };

    // Filter positions into active and desired - fixed type annotations
    const activePositions = tradingData.positions.filter(isPositionActive);
    const desiredPositions = tradingData.positions.filter((pos: Position) => !isPositionActive(pos));

    // Helper functions
    const truncateOrderId = (orderId: string): string => {
        if (!orderId) return 'N/D';
        if (orderId.length <= 8) return orderId;
        return `${orderId.substring(0, 4)}...${orderId.substring(orderId.length - 3)}`;
    };

    const formatSpread = (spread?: number) => {
        if (spread === undefined || spread === null) return '-';
        return (spread * 100).toFixed(3) + '%';
    };

    const isValidTimestamp = (timestamp: string): boolean => {
        if (!timestamp) return false;
        if (timestamp.startsWith('0001-01-01')) return false;
        const date = new Date(timestamp);
        return !isNaN(date.getTime()) && date.getFullYear() > 1970;
    };

    const calculateAge = (timestamp: string) => {
        if (!isValidTimestamp(timestamp)) return 'N/D';

        const referenceTime = ts;
        const openTime = new Date(timestamp).getTime();
        const diffMs = referenceTime - openTime;
        const diffSec = Math.floor(diffMs / 1000);

        if (diffSec < 0) return '0s';
        if (diffSec < 60) return `${diffSec}s`;

        const diffMin = Math.floor(diffSec / 60);
        const remainingSec = diffSec % 60;
        if (diffMin < 60) return `${diffMin}m ${remainingSec}s`;

        const diffHours = Math.floor(diffMin / 60);
        const remainingMin = diffMin % 60;
        if (diffHours < 24) return `${diffHours}h ${remainingMin}m`;

        const diffDays = Math.floor(diffHours / 24);
        const remainingHours = diffHours % 24;
        return `${diffDays}d ${remainingHours}h`;
    };

    // Handle copy to clipboard
    const handleCopyOrderId = (orderId: string) => {
        if (!orderId) return;

        navigator.clipboard.writeText(orderId)
            .then(() => {
                setCopiedOrderId(orderId);
                setTimeout(() => setCopiedOrderId(null), 1500);
            })
            .catch(err => {
                console.error('Failed to copy order ID:', err);
            });
    };

    // Render an individual order card
    const renderOrderCard = (position: Position, isActive: boolean) => {
        return (
            <div key={position.id} className={cx(styles.orderCard, isActive ? styles.activeOrder : styles.desiredOrder)}>
                <div className={styles.orderHeader}>
                    <div className={styles.orderStatus}>
            <span className={cx(styles.statusBadge, isActive ? styles.activeStatus : styles.desiredStatus)}>
              {isActive ? 'Active' : 'Desired'}
            </span>
                        <span className={cx(styles.sideBadge, position.side === 'bid' ? styles.bidSide : styles.askSide)}>
              {position.side === 'bid' ? 'BID' : 'ASK'}
            </span>
                    </div>
                    <div
                        className={cx(styles.orderIdDisplay, copiedOrderId === position.order_id ? styles.orderIdCopied : '')}
                        onClick={() => handleCopyOrderId(position.order_id)}
                    >
                        {truncateOrderId(position.order_id)}
                        {copiedOrderId === position.order_id ? (
                            <span className={styles.copiedIcon}>✓</span>
                        ) : (
                            <span className={styles.copyIcon}>📋</span>
                        )}
                    </div>
                </div>

                <div className={styles.orderDetails}>
                    <div className={styles.detailRow}>
                        <div className={styles.detailLabel}>Size</div>
                        <div className={styles.detailValue}>
                            {formatAmount(position.amount, decimalPlaces.amount)}
                        </div>
                    </div>

                    {isActive && position.left_amount !== undefined && (
                        <div className={styles.detailRow}>
                            <div className={styles.detailLabel}>Left</div>
                            <div className={styles.detailValue}>
                                {formatAmount(position.left_amount, decimalPlaces.amount)}
                            </div>
                        </div>
                    )}

                    <div className={styles.detailRow}>
                        <div className={styles.detailLabel}>Entry</div>
                        <div className={cx(styles.detailValue, position.side === 'bid' ? styles.buyValue : styles.sellValue)}>
                            {formatPrice(position.price, decimalPlaces.price)}
                        </div>
                    </div>

                    <div className={styles.detailRow}>
                        <div className={styles.detailLabel}>Ref %</div>
                        <div className={styles.detailValue}>
                            {formatSpread(position.ref_spread)}
                        </div>
                    </div>

                    <div className={styles.detailRow}>
                        <div className={styles.detailLabel}>Top %</div>
                        <div className={styles.detailValue}>
                            {formatSpread(position.top_spread)}
                        </div>
                    </div>

                    <div className={styles.detailRow}>
                        <div className={styles.detailLabel}>Age</div>
                        <div className={styles.detailValue}>
                            {calculateAge(position.opened_at)}
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className={styles.container}>
            <div className={styles.sectionHeader}>ACTIVE ORDERS ({activePositions.length})</div>

            <div className={styles.cardList}>
                {activePositions.length > 0 ? (
                    activePositions.map((position: Position) => renderOrderCard(position, true))
                ) : (
                    <div className={styles.noActiveOrders}>No active orders</div>
                )}
            </div>

            {desiredPositions.length > 0 && (
                <>
                    <div className={styles.sectionHeader}>DESIRED ORDERS ({desiredPositions.length})</div>
                    <div className={styles.cardList}>
                        {desiredPositions.map((position: Position) => renderOrderCard(position, false))}
                    </div>
                </>
            )}
        </div>
    );
};

const getStyles = () => ({
    container: css`
    display: flex;
    flex-direction: column;
    padding: 10px;
    overflow-y: auto;
    height: 100%;
  `,
    sectionHeader: css`
    font-size: 12px;
    font-weight: 500;
    color: #9096a1;
    margin: 5px 0;
    padding-left: 4px;
  `,
    cardList: css`
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
  `,
    orderCard: css`
    background-color: rgba(18, 23, 34, 0.6);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  `,
    activeOrder: css`
    border-left: 3px solid #00b164;
  `,
    desiredOrder: css`
    border-left: 3px solid #5794F2;
    opacity: 0.85;
  `,
    orderHeader: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: rgba(26, 32, 44, 0.7);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  `,
    orderStatus: css`
    display: flex;
    gap: 6px;
  `,
    statusBadge: css`
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
  `,
    activeStatus: css`
    background-color: rgba(0, 177, 100, 0.2);
    color: #00b164;
  `,
    desiredStatus: css`
    background-color: rgba(87, 148, 242, 0.2);
    color: #5794F2;
  `,
    sideBadge: css`
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
  `,
    bidSide: css`
    background-color: rgba(0, 177, 100, 0.1);
    color: #00b164;
  `,
    askSide: css`
    background-color: rgba(234, 0, 112, 0.1);
    color: #ea0070;
  `,
    orderIdDisplay: css`
    font-family: monospace;
    font-size: 11px;
    padding: 3px 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    color: #9096a1;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }
  `,
    orderIdCopied: css`
    background-color: rgba(0, 177, 100, 0.2) !important;
    color: #00b164;
  `,
    copiedIcon: css`
    font-size: 10px;
    color: #00b164;
  `,
    copyIcon: css`
    font-size: 10px;
    opacity: 0.5;
  `,
    orderDetails: css`
    padding: 10px 12px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  `,
    detailRow: css`
    display: flex;
    flex-direction: column;
  `,
    detailLabel: css`
    font-size: 10px;
    color: #9096a1;
    margin-bottom: 2px;
  `,
    detailValue: css`
    font-size: 13px;
  `,
    buyValue: css`
    color: #00b164;
  `,
    sellValue: css`
    color: #ea0070;
  `,
    noData: css`
    padding: 30px;
    text-align: center;
    color: #9096a1;
    font-size: 14px;
  `,
    noActiveOrders: css`
    padding: 15px;
    text-align: center;
    color: #9096a1;
    font-style: italic;
    font-size: 13px;
    background-color: rgba(18, 23, 34, 0.3);
    border-radius: 4px;
  `,
});
