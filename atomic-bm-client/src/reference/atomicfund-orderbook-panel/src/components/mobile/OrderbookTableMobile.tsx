import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { OrderEntry } from '../../types';
import { formatPrice, formatAmount } from '../../utils/formatters';

interface OrderbookTableMobileProps {
    buyOrders: OrderEntry[];
    sellOrders: OrderEntry[];
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
}

export const OrderbookTableMobile: React.FC<OrderbookTableMobileProps> = ({
                                                                              buyOrders,
                                                                              sellOrders,
                                                                              decimalPlaces,
                                                                          }) => {
    const styles = useStyles2(getStyles);

    // Limit the number of orders to show on mobile
    const maxMobileOrders = 7;
    const limitedBuyOrders = buyOrders.slice(0, maxMobileOrders);
    const limitedSellOrders = sellOrders.slice(0, maxMobileOrders);

    // Calculate total amount for depth visualization
    const maxBuyAmount = Math.max(...limitedBuyOrders.map(order => order.amount), 0);
    const maxSellAmount = Math.max(...limitedSellOrders.map(order => order.amount), 0);

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <div className={styles.buyHeader}>
                    <div className={styles.amountHeader}>Amount</div>
                    <div className={styles.priceHeader}>Price</div>
                </div>
                <div className={styles.sellHeader}>
                    <div className={styles.priceHeader}>Price</div>
                    <div className={styles.amountHeader}>Amount</div>
                </div>
            </div>

            <div className={styles.orderbookContent}>
                <div className={styles.buyOrdersContainer}>
                    {limitedBuyOrders.map((order, index) => {
                        const depthWidth = (order.amount / maxBuyAmount) * 100;
                        return (
                            <div key={`buy-${index}`} className={styles.orderRow}>
                                {/* Background depth visualization */}
                                <div
                                    className={styles.buyDepth}
                                    style={{ width: `${depthWidth}%` }}
                                />

                                {/* Order content */}
                                <div className={styles.orderContent}>
                                    <div className={styles.amountValue}>
                                        {formatAmount(order.amount, decimalPlaces.amount)}
                                    </div>
                                    <div className={cx(styles.priceValue, styles.buyPrice)}>
                                        {formatPrice(order.price, decimalPlaces.price)}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                <div className={styles.sellOrdersContainer}>
                    {limitedSellOrders.map((order, index) => {
                        const depthWidth = (order.amount / maxSellAmount) * 100;
                        return (
                            <div key={`sell-${index}`} className={styles.orderRow}>
                                {/* Background depth visualization */}
                                <div
                                    className={styles.sellDepth}
                                    style={{ width: `${depthWidth}%` }}
                                />

                                {/* Order content */}
                                <div className={styles.orderContent}>
                                    <div className={cx(styles.priceValue, styles.sellPrice)}>
                                        {formatPrice(order.price, decimalPlaces.price)}
                                    </div>
                                    <div className={styles.amountValue}>
                                        {formatAmount(order.amount, decimalPlaces.amount)}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

const getStyles = () => ({
    container: css`
    display: flex;
    flex-direction: column;
    background-color: #0b0f19;
    flex: 1;
  `,
    header: css`
    display: flex;
    background-color: #121722;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    padding: 8px 0;
  `,
    buyHeader: css`
    display: flex;
    flex: 1;
    padding: 0 8px;
  `,
    sellHeader: css`
    display: flex;
    flex: 1;
    padding: 0 8px;
  `,
    amountHeader: css`
    flex: 1;
    color: #9096a1;
    font-size: 11px;
    text-align: center;
  `,
    priceHeader: css`
    flex: 1;
    color: #9096a1;
    font-size: 11px;
    text-align: center;
  `,
    orderbookContent: css`
    display: flex;
    flex: 1;
  `,
    buyOrdersContainer: css`
    display: flex;
    flex-direction: column;
    flex: 1;
    border-right: 1px solid rgba(255, 255, 255, 0.05);
  `,
    sellOrdersContainer: css`
    display: flex;
    flex-direction: column;
    flex: 1;
  `,
    orderRow: css`
    position: relative;
    height: 30px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.02);

    &:nth-child(odd) {
      background-color: rgba(26, 32, 44, 0.3);
    }
  `,
    buyDepth: css`
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(to left, rgba(0, 177, 100, 0.3), rgba(0, 177, 100, 0.15));
    z-index: 0;
  `,
    sellDepth: css`
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(to right, rgba(234, 0, 112, 0.3), rgba(234, 0, 112, 0.15));
    z-index: 0;
  `,
    orderContent: css`
    display: flex;
    width: 100%;
    z-index: 1;
    padding: 0 8px;
  `,
    amountValue: css`
    flex: 1;
    text-align: center;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,
    priceValue: css`
    flex: 1;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `,
    buyPrice: css`
    color: #00b164;
  `,
    sellPrice: css`
    color: #ea0070;
  `,
});
