import React from 'react';
import { css, cx } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { formatPrice, formatAmount, formatTotal } from '../../utils/formatters';

interface ExecStat {
    buy: {
        avg_price: number;
        money_volume: number;
        volume: number;
    };
    sell: {
        avg_price: number;
        money_volume: number;
        volume: number;
    };
    time_frame: number;
}

interface ExecutionStatsMobileProps {
    tradingData: any;
    decimalPlaces: {
        price: number;
        amount: number;
        total: number;
    };
}

// Helper function to format time frames
const formatTimeFrame = (nanoseconds: number): string => {
    // Convert nanoseconds to seconds
    const seconds = nanoseconds / 1000000000;

    if (seconds === 300) return '5m';
    if (seconds === 3600) return '1h';
    if (seconds === 43200) return '12h';
    if (seconds === 86400) return '24h';
    if (seconds === 604800) return '7d';

    // Fallback for unknown time frames
    return `${seconds}s`;
};

export const ExecutionStatsMobile: React.FC<ExecutionStatsMobileProps> = ({
                                                                              tradingData,
                                                                              decimalPlaces,
                                                                          }) => {
    const styles = useStyles2(getStyles);

    if (!tradingData || !tradingData.exec_stats || !Array.isArray(tradingData.exec_stats) || tradingData.exec_stats.length === 0) {
        return (
            <div className={styles.container}>
                <div className={styles.noData}>No execution statistics available.</div>
            </div>
        );
    }

    // Sort stats by time frame (ascending)
    const sortedStats = [...tradingData.exec_stats].sort((a, b) => a.time_frame - b.time_frame);

    return (
        <div className={styles.container}>
            <div className={styles.sectionHeader}>EXECUTION STATS</div>

            {/* Cards for each time frame */}
            <div className={styles.cardsContainer}>
                {sortedStats.map((stat: ExecStat, index: number) => {
                    const hasBuyData = stat.buy.volume > 0;
                    const hasSellData = stat.sell.volume > 0;
                    const hasData = hasBuyData || hasSellData;

                    if (!hasData) return null;

                    // Calculate the data we need
                    const totalVolume = stat.buy.volume + stat.sell.volume;

                    // Calculate spread
                    let spread = 0;
                    let spreadPercent = 0;
                    if (hasBuyData && hasSellData && stat.buy.avg_price > 0) {
                        spread = stat.sell.avg_price - stat.buy.avg_price;
                        spreadPercent = (spread / stat.buy.avg_price) * 100;
                    }

                    // Calculate net position
                    const netPosition = stat.buy.volume - stat.sell.volume;

                    // Calculate PnL
                    const pnl = stat.sell.money_volume - stat.buy.money_volume;

                    return (
                        <div key={`stat-${index}`} className={styles.statCard}>
                            <div className={styles.cardHeader}>
                                {formatTimeFrame(stat.time_frame)}
                            </div>
                            <div className={styles.cardContent}>
                                <div className={styles.statRow}>
                                    <div className={styles.statLabel}>Volume</div>
                                    <div className={styles.statValue}>
                                        {formatAmount(totalVolume, decimalPlaces.amount)}
                                    </div>
                                </div>

                                <div className={styles.bidAskRow}>
                                    <div className={cx(styles.bidCell)}>
                                        <div className={styles.statLabel}>Avg Bid</div>
                                        <div className={cx(styles.statValue, styles.buyValue)}>
                                            {hasBuyData
                                                ? formatPrice(stat.buy.avg_price, decimalPlaces.price)
                                                : '-'}
                                        </div>
                                    </div>
                                    <div className={styles.bidAskSeparator}>↔</div>
                                    <div className={cx(styles.askCell)}>
                                        <div className={styles.statLabel}>Avg Ask</div>
                                        <div className={cx(styles.statValue, styles.sellValue)}>
                                            {hasSellData
                                                ? formatPrice(stat.sell.avg_price, decimalPlaces.price)
                                                : '-'}
                                        </div>
                                    </div>
                                </div>

                                <div className={styles.statRow}>
                                    <div className={styles.statLabel}>Spread</div>
                                    <div className={styles.statValue}>
                                        {(hasBuyData && hasSellData)
                                            ? `${spreadPercent.toFixed(2)}%`
                                            : '-'}
                                    </div>
                                </div>

                                <div className={styles.statRow}>
                                    <div className={styles.statLabel}>Net Position</div>
                                    <div className={cx(
                                        styles.statValue,
                                        netPosition > 0 ? styles.buyValue : netPosition < 0 ? styles.sellValue : ''
                                    )}>
                                        {formatAmount(Math.abs(netPosition), decimalPlaces.amount)}
                                        {netPosition !== 0 && (
                                            <span className={styles.indicator}>
                                                {netPosition > 0 ? '↑' : '↓'}
                                            </span>
                                        )}
                                    </div>
                                </div>

                                <div className={styles.statRow}>
                                    <div className={styles.statLabel}>PnL</div>
                                    <div className={cx(
                                        styles.statValue,
                                        pnl > 0 ? styles.buyValue : pnl < 0 ? styles.sellValue : ''
                                    )}>
                                        {formatTotal(Math.abs(pnl), decimalPlaces.total)}
                                        {pnl !== 0 && (
                                            <span className={styles.indicator}>
                                                {pnl > 0 ? '+' : '-'}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

const getStyles = () => ({
    container: css`
        display: flex;
        flex-direction: column;
        padding: 10px;
        overflow-y: auto;
    `,
    sectionHeader: css`
        font-size: 12px;
        font-weight: 500;
        color: #9096a1;
        margin-bottom: 10px;
        padding-left: 4px;
    `,
    cardsContainer: css`
        display: flex;
        flex-direction: column;
        gap: 10px;
    `,
    statCard: css`
        background-color: rgba(18, 23, 34, 0.6);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        overflow: hidden;
    `,
    cardHeader: css`
        padding: 8px 12px;
        background-color: rgba(26, 32, 44, 0.7);
        font-size: 11px;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    `,
    cardContent: css`
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 8px;
    `,
    statRow: css`
        display: flex;
        justify-content: space-between;
        align-items: center;
    `,
    bidAskRow: css`
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        background-color: rgba(0, 0, 0, 0.15);
        border-radius: 4px;
    `,
    bidCell: css`
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 8px;
    `,
    askCell: css`
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 8px;
    `,
    bidAskSeparator: css`
        color: rgba(255, 255, 255, 0.5);
        font-size: 14px;
    `,
    statLabel: css`
        font-size: 11px;
        color: #9096a1;
    `,
    statValue: css`
        font-size: 13px;
        font-weight: 500;
        font-variant-numeric: tabular-nums;
    `,
    buyValue: css`
        color: #00b164;
    `,
    sellValue: css`
        color: #ea0070;
    `,
    indicator: css`
        margin-left: 4px;
        font-size: 10px;
    `,
    noData: css`
        padding: 30px;
        text-align: center;
        color: #9096a1;
        font-size: 14px;
    `,
});
