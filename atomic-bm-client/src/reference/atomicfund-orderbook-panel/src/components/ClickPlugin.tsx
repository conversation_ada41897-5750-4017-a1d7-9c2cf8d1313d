import React, { useLayoutEffect } from 'react';
import { UPlotConfigBuilder } from '@grafana/ui';
import uPlot from 'uplot';

interface ClickPluginProps {
    config: UPlotConfigBuilder;
    onClickPoint: (timestamp: number) => void;
}

export const ClickPlugin: React.FC<ClickPluginProps> = ({ config, onClickPoint }) => {
    useLayoutEffect(() => {
        config.addHook('init', (u: uPlot) => {
            u.over.addEventListener('click', (e: MouseEvent) => {
                // Get the plot's dimensions
                const rect = u.over.getBoundingClientRect();
                // Convert client coordinates to uPlot coordinates
                const x = e.clientX - rect.left;

                // Find the closest data point index to the x coordinate
                const idx = u.posToIdx(x);

                if (idx !== null && idx >= 0) {
                    // Get the timestamp at this index
                    const timestamp = u.data[0][idx];
                    if (timestamp !== null) {
                        onClickPoint(timestamp);
                    }
                }
            });
        });
    }, [config, onClickPoint]);

    return null;
};