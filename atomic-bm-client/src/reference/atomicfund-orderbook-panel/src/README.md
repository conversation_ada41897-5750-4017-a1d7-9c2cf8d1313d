# Orderbook Panel for Grafana

A Grafana panel plugin that displays cryptocurrency orderbook data with buy and sell orders, depth visualization, and market summary information.

## Features

- Display buy (bid) and sell (ask) orders with price, amount, and total values
- Visual depth representation for each side of the orderbook
- Price indicator showing the latest price and spread information
- Buy/Sell ratio visualization
- Hover tooltips showing summary statistics
- Customizable display options

## Data Format

The plugin expects data in the following format:

1. First series: Buy orders with price and amount fields
2. Second series: Sell orders with price and amount fields

Each data frame should contain at least:
- A price field (labeled as "price", "Price", "value", or "Value")
- An amount field (labeled as "amount", "Amount", "volume", or "Volume")

## Configuration Options

- **Base Asset**: The cryptocurrency being bought/sold (e.g., BTC)
- **Quote Asset**: The currency used for pricing (e.g., USDT)
- **Price Decimal Places**: Number of decimal places to display for prices
- **Amount Decimal Places**: Number of decimal places to display for amounts
- **Total Decimal Places**: Number of decimal places to display for totals
- **Depth Visualization**: Choose between "Amount" or "Cumulative" for the depth visualization
- **Max Orders**: Maximum number of orders to display on each side
- **Show Summary**: Toggle to show/hide the buy/sell ratio indicator at the bottom

## Example Queries

For Prometheus or other time series databases, you'll need to transform your data to match the expected format. The plugin works best with properly structured data that includes price and amount information for both buy and sell orders.