import { PanelPlugin } from '@grafana/data';
import { OrderbookOptions } from './types';
import { OrderbookPanel } from './components/OrderbookPanel';

export const plugin = new PanelPlugin<OrderbookOptions>(OrderbookPanel).setPanelOptions((builder) => {
    return builder
        // Orderbook display settings
        .addNumberInput({
            path: 'decimalPlaces.price',
            name: 'Price Decimal Places',
            description: 'Number of decimal places to display for prices',
            defaultValue: 2,
            category: ['Display'],
        })
        .addNumberInput({
            path: 'decimalPlaces.amount',
            name: 'Amount Decimal Places',
            description: 'Number of decimal places to display for amounts',
            defaultValue: 5,
            category: ['Display'],
        })
        .addNumberInput({
            path: 'decimalPlaces.total',
            name: 'Total Decimal Places',
            description: 'Number of decimal places to display for totals',
            defaultValue: 2,
            category: ['Display'],
        })
        .addNumberInput({
            path: 'maxOrders',
            name: 'Max Orders',
            description: 'Maximum number of orders to display on each side',
            defaultValue: 15,
            category: ['Display'],
        })
        // Summary and information display
        .addBooleanSwitch({
            path: 'showSummary',
            name: 'Show Summary',
            description: 'Display the summary statistics bar at the bottom',
            defaultValue: true,
            category: ['Information'],
        })
        // Trading metrics settings
        .addBooleanSwitch({
            path: 'showTradingMetrics',
            name: 'Show Trading Metrics',
            description: 'Display detailed trading metrics from bot data',
            defaultValue: true,
            category: ['Trading Metrics'],
        })
        // Playback and time navigation settings
        .addBooleanSwitch({
            path: 'enableTimeNavigation',
            name: 'Enable Time Navigation',
            description: 'Enable navigation through historical snapshots',
            defaultValue: true,
            category: ['Playback'],
        })
        .addBooleanSwitch({
            path: 'enablePlayback',
            name: 'Enable Playback',
            description: 'Enable orderbook playback animation',
            defaultValue: true,
            showIf: (config) => config.enableTimeNavigation === true,
            category: ['Playback'],
        });
});
