// Order entry in the orderbook
export interface OrderEntry {
  price: number;
  amount: number;
  total: number;
  hasOpenOrder?: boolean;  // Flag for orders with open status (from trading data)
  refSpread?: number;      // Reference spread from trading data
  topSpread?: number;      // Top spread from trading data
}

// Possible price direction values
export type PriceDirection = 'up' | 'down' | 'neutral';

// Summary information
export interface OrderbookSummary {
  avgPrice: number;
  totalBaseAmount: number;
  totalQuoteValue: number;
  buyPercentage: number;
  sellPercentage: number;
  lastPrice: number;
  spread: number;
  spreadPercentage: number;
  // Separate volumes for buy and sell sides
  buyVolume: number;          // Total quote value for buys
  sellVolume: number;         // Total base asset amount for buys
  buyAmount: number;          // Total base asset amount for buys
  sellAmount: number;         // Total base asset amount for sells
  priceDirection?: PriceDirection;

  // Additional fields from trading data
  bestBuyPrice?: number;      // Best buy price from reference market
  bestSellPrice?: number;     // Best sell price from reference market
  buySpread?: number;         // Buy spread from trading data
  sellSpread?: number;        // Sell spread from trading data
  lastUpdated?: string;       // Timestamp of last update from book_last_updated
}

export interface OrderbookOptions {
  decimalPlaces: {
    price: number;
    amount: number;
    total: number;
  };
  maxOrders: number;          // Number of orders to display on each side
  showSummary: boolean;
  enableTimeNavigation: boolean; // Enable time navigation with historical snapshots
  enablePlayback: boolean;    // Enable orderbook playback functionality
  // New options for trading metrics
  showTradingMetrics?: boolean; // Show trading metrics panel
  // Animation settings
  enableAnimations?: boolean; // Enable animations for price and amount changes
}