import { useState, useEffect, useCallback, useRef } from 'react';

interface UsePlaybackResult {
    isPlaying: boolean;
    currentTimestampIndex: number;
    playbackSpeed: number;
    handlePlay: () => void;
    handlePause: () => void;
    handleStop: () => void;
    handleSpeedChange: (speed: number) => void;
    handleSeek: (index: number, skipCallback?: boolean) => void;
}

export function usePlayback(
    timestamps: number[],
    isLiveMode: boolean,
    onTimestampChange: (timestamp: number | null) => void
): UsePlaybackResult {
    // Initialize with last index if timestamps are available
    const initialIndex = timestamps.length > 0 ? timestamps.length - 1 : 0;

    const [currentTimestampIndex, setCurrentTimestampIndex] = useState(initialIndex);
    const [isPlaying, setIsPlaying] = useState(false);
    const [playbackSpeed, setPlaybackSpeed] = useState(1);

    // Use a ref to store the interval ID to avoid issues with cleanup
    const intervalRef = useRef<number | null>(null);

    // Track previous timestamp length for proper update handling
    const prevTimestampsLengthRef = useRef(timestamps.length);

    // Add a ref to track initial load state
    const isInitialLoadRef = useRef(true);

    // Use a ref to avoid circular dependencies between callbacks
    const stateRef = useRef({
        isPlaying,
        playbackSpeed,
        currentTimestampIndex,
        timestamps,
    });

    // Update the ref when state changes
    useEffect(() => {
        stateRef.current = {
            isPlaying,
            playbackSpeed,
            currentTimestampIndex,
            timestamps,
        };
    }, [isPlaying, playbackSpeed, currentTimestampIndex, timestamps]);

    // Handle timestamps array changes - don't reset to end during playback
    useEffect(() => {
        // Only update if timestamps length has changed (new data arrived)
        if (timestamps.length !== prevTimestampsLengthRef.current) {
            const newTimestampsAdded = timestamps.length > prevTimestampsLengthRef.current;

            // Update the reference to the new length
            prevTimestampsLengthRef.current = timestamps.length;

            // If not playing, jump to latest index
            if (!isPlaying && newTimestampsAdded) {
                const newIndex = timestamps.length > 0 ? timestamps.length - 1 : 0;
                setCurrentTimestampIndex(newIndex);

                // Don't call onTimestampChange on initial load to avoid switching to historical mode
                if (timestamps.length > 0 && !isInitialLoadRef.current) {
                    // Key fix: Only update with timestamp if already in historical mode
                    // This prevents switching to historical mode when refresh happens in live mode
                    if (!isLiveMode) {
                        onTimestampChange(timestamps[newIndex]);
                    }
                    // No need to call onTimestampChange(null) if already in live mode
                }
            }

            // If no data or first load, set to end
            else if (prevTimestampsLengthRef.current === 0) {
                const newIndex = timestamps.length > 0 ? timestamps.length - 1 : 0;
                setCurrentTimestampIndex(newIndex);
            }

            // Mark that initial load is completed
            isInitialLoadRef.current = false;
        }
    }, [timestamps, isPlaying, isLiveMode, onTimestampChange]);

    // Clean up interval on unmount
    useEffect(() => {
        return () => {
            if (intervalRef.current !== null) {
                window.clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, []);

    // Handle stop functionality
    const handleStop = useCallback(() => {
        setIsPlaying(false);

        // Set to latest timestamp on stop
        const newIndex = timestamps.length > 0 ? timestamps.length - 1 : 0;
        setCurrentTimestampIndex(newIndex);

        if (intervalRef.current !== null) {
            window.clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        // Signal to go back to live mode
        onTimestampChange(null);
    }, [timestamps, onTimestampChange]);

    // Handle pause functionality
    const handlePause = useCallback(() => {
        setIsPlaying(false);

        if (intervalRef.current !== null) {
            window.clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    }, []);

    // Handle play functionality
    const handlePlay = useCallback(() => {
        if (stateRef.current.timestamps.length === 0) {
            return;
        }

        setIsPlaying(true);

        // Clear any existing interval
        if (intervalRef.current !== null) {
            window.clearInterval(intervalRef.current);
        }

        // Create a new interval that advances through timestamps
        intervalRef.current = window.setInterval(() => {
            setCurrentTimestampIndex((prevIndex) => {
                // Get the latest timestamps array from the ref
                const currentTimestamps = stateRef.current.timestamps;
                const nextIndex = prevIndex + 1;

                // If we reach the end, stop playback and go back to live mode
                if (nextIndex >= currentTimestamps.length) {
                    // Call handleStop directly rather than through the callback
                    setIsPlaying(false);
                    if (intervalRef.current !== null) {
                        window.clearInterval(intervalRef.current);
                        intervalRef.current = null;
                    }

                    // Add the callback with null to indicate we should return to live mode
                    // This is a signal to the parent component
                    onTimestampChange(null);

                    const finalIndex = currentTimestamps.length - 1;
                    return finalIndex; // Keep at last index instead of resetting to 0
                }

                // Update the selected timestamp to show data from this point
                onTimestampChange(currentTimestamps[nextIndex]);
                return nextIndex;
            });
        }, 1000 / stateRef.current.playbackSpeed); // Adjust speed based on playbackSpeed

    }, [onTimestampChange]);

    // Handle speed change
    const handleSpeedChange = useCallback((speed: number) => {
        setPlaybackSpeed(speed);

        // If currently playing, restart the interval with the new speed
        if (stateRef.current.isPlaying && intervalRef.current !== null) {
            window.clearInterval(intervalRef.current);

            intervalRef.current = window.setInterval(() => {
                setCurrentTimestampIndex((prevIndex) => {
                    const currentTimestamps = stateRef.current.timestamps;
                    const nextIndex = prevIndex + 1;

                    if (nextIndex >= currentTimestamps.length) {
                        // Call handleStop logic directly
                        setIsPlaying(false);
                        if (intervalRef.current !== null) {
                            window.clearInterval(intervalRef.current);
                            intervalRef.current = null;
                        }

                        // Signal to return to live mode
                        onTimestampChange(null);

                        const finalIndex = currentTimestamps.length - 1;
                        return finalIndex; // Keep at last index
                    }

                    onTimestampChange(currentTimestamps[nextIndex]);
                    return nextIndex;
                });
            }, 1000 / speed);
        }
    }, [onTimestampChange]);

    // Handle seeking to a specific timestamp
    const handleSeek = useCallback((index: number, skipCallback = false) => {
        // Validate the index is within bounds
        if (index < 0) {
            index = 0;
        } else if (timestamps.length > 0 && index >= timestamps.length) {
            index = timestamps.length - 1;
        }

        setCurrentTimestampIndex(index);

        // Only trigger timestamp change if there are timestamps available
        // AND we're not skipping the callback
        if (timestamps.length > 0 && !skipCallback) {
            onTimestampChange(timestamps[index]);
        }
    }, [timestamps, onTimestampChange]);

    return {
        isPlaying,
        currentTimestampIndex,
        playbackSpeed,
        handlePlay,
        handlePause,
        handleStop,
        handleSpeedChange,
        handleSeek
    };
}
