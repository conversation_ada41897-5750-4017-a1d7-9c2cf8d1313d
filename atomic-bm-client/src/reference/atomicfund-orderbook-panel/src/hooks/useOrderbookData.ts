import { useState, useEffect, useRef } from 'react';
import { DataFrame, dateTime } from '@grafana/data';
import { OrderbookOptions, OrderEntry, OrderbookSummary } from '../types';
import { OrderbookDataProcessor } from '../data/OrderbookDataProcessor';

export function useOrderbookData(
    dataSeries: DataFrame[],
    options: OrderbookOptions,
    isLiveData: boolean,
    selectedTimestamp: number | null
): {
    buyOrders: OrderEntry[];
    sellOrders: OrderEntry[];
    summary: OrderbookSummary;
    timestamps: number[];
    lastUpdateTime: string;
    hasTradingData: boolean;
    tradingData: any;
    isMarketSnapshot: boolean;
} {
    const [buyOrders, setBuyOrders] = useState<OrderEntry[]>([]);
    const [sellOrders, setSellOrders] = useState<OrderEntry[]>([]);
    const [timestamps, setTimestamps] = useState<number[]>([]);
    const [lastUpdateTime, setLastUpdateTime] = useState<string>('');
    const [hasTradingData, setHasTradingData] = useState<boolean>(false);
    const [tradingData, setTradingData] = useState<any>(null);
    const [isMarketSnapshot, setIsMarketSnapshot] = useState<boolean>(false);

    // Track the previous price for direction determination
    const prevPriceRef = useRef<number>(0);

    const [summary, setSummary] = useState<OrderbookSummary>({
        avgPrice: 0,
        totalBaseAmount: 0,
        totalQuoteValue: 0,
        buyPercentage: 50,
        sellPercentage: 50,
        lastPrice: 0,
        spread: 0,
        spreadPercentage: 0,
        buyVolume: 0,
        sellVolume: 0,
        buyAmount: 0,
        sellAmount: 0,
        priceDirection: 'neutral'
    });

    // Extract timestamps for playback functionality
    useEffect(() => {
        if (dataSeries.length === 0) {
            return;
        }

        // Extract timestamps from data
        const extractedTimestamps = OrderbookDataProcessor.extractTimestamps(dataSeries);
        setTimestamps(extractedTimestamps.sort((a, b) => a - b));
    }, [dataSeries]);

    // Process data when it changes or view mode changes
    useEffect(() => {
        if (dataSeries.length === 0) {
            return;
        }

        let processedData;
        if (isLiveData) {
            // Use the most recent data
            processedData = OrderbookDataProcessor.processData(dataSeries);
        } else if (selectedTimestamp !== null) {
            // Use the data from the selected timestamp
            const filteredData = OrderbookDataProcessor.filterByTimestamp(dataSeries, selectedTimestamp);
            processedData = OrderbookDataProcessor.processData(filteredData);
        } else {
            return;
        }

        // Update market snapshot flag
        setIsMarketSnapshot(!!processedData.isMarketSnapshot);

        // Store the trading data
        setTradingData(processedData.tradingData);

        // Check if we have trading data
        setHasTradingData(!!processedData.tradingData);

        // Limit the number of orders to show
        const limitedBuyOrders = processedData.buyOrders.slice(0, options.maxOrders);
        const limitedSellOrders = processedData.sellOrders.slice(0, options.maxOrders);

        // Calculate summary stats based on the limited orders that are visible in the UI
        const summaryStat = OrderbookDataProcessor.calculateSummary(
            limitedBuyOrders,
            limitedSellOrders,
            processedData.tradingData,
            prevPriceRef.current
        );

        // Update previous price reference after using it for comparison
        if (summaryStat.avgPrice !== 0) {
            prevPriceRef.current = summaryStat.avgPrice;
        }

        // Update state
        setBuyOrders(limitedBuyOrders);
        setSellOrders(limitedSellOrders);
        setSummary(summaryStat);

        // Update last update time
        if (isLiveData) {
            // First check for lastUpdated in summary (from trading data)
            if (summaryStat.lastUpdated) {
                setLastUpdateTime(summaryStat.lastUpdated);
            } else {
                // Fallback to ts field
                const frameWithTime = dataSeries.find(
                    series => series.fields.some(field => field.name === 'ts')
                );

                if (frameWithTime) {
                    const timeField = frameWithTime.fields.find(field => field.name === 'ts');
                    if (timeField && timeField.values.length > 0) {
                        const lastTime = timeField.values[0];
                        setLastUpdateTime(dateTime(lastTime).format('YYYY-MM-DD HH:mm:ss'));
                    }
                }
            }
        }
    }, [dataSeries, options.maxOrders, isLiveData, selectedTimestamp]);

    return {
        buyOrders,
        sellOrders,
        summary,
        timestamps,
        lastUpdateTime,
        hasTradingData,
        tradingData,
        isMarketSnapshot
    };
}
