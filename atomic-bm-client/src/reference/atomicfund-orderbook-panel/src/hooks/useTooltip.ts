import { useState, useCallback } from 'react';
import { OrderEntry } from '../types';
import { calculateTooltipValues as calculateValues } from '../utils/calculations';

interface TooltipState {
    visible: boolean;
    x: number;
    y: number;
    side: 'buy' | 'sell';
    index: number;
}

interface UseTooltipResult {
    tooltip: TooltipState;
    handleMouseEnter: (
        side: 'buy' | 'sell',
        index: number,
        event: React.MouseEvent
    ) => void;
    handleMouseLeave: () => void;
    calculateTooltipValues: (
        side: 'buy' | 'sell',
        index: number
    ) => {
        avgPrice: number;
        sumBaseAmount: number;
        sumQuoteValue: number
    };
}

export function useTooltip(buyOrders: OrderEntry[], sellOrders: OrderEntry[]): UseTooltipResult {
    const [tooltip, setTooltip] = useState<TooltipState>({
        visible: false,
        x: 0,
        y: 0,
        side: 'buy',
        index: -1,
    });

    const handleMouseEnter = useCallback((
        side: 'buy' | 'sell',
        index: number,
        event: React.MouseEvent
    ) => {
        // Get the panel bounds
        const panelElement = event.currentTarget.closest('[data-testid="orderbook-wrapper"]');
        const panelRect = panelElement?.getBoundingClientRect();

        if (!panelRect) {
            return;
        }

        // Calculate position relative to the panel
        const mouseX = event.clientX - panelRect.left;
        const mouseY = event.clientY - panelRect.top;

        // Set tooltip position with boundaries
        let tooltipX = side === 'buy' ? mouseX + 15 : mouseX - 195; // Account for tooltip width
        let tooltipY = mouseY + 15;

        // Ensure tooltip stays within panel bounds
        const tooltipWidth = 180; // Width from CSS
        const tooltipHeight = 120; // Approximate height

        // Adjust X position if needed
        if (tooltipX + tooltipWidth > panelRect.width) {
            tooltipX = mouseX - tooltipWidth - 15;
        }
        if (tooltipX < 0) {
            tooltipX = 15;
        }

        // Adjust Y position if needed
        if (tooltipY + tooltipHeight > panelRect.height) {
            tooltipY = mouseY - tooltipHeight - 15;
        }
        if (tooltipY < 0) {
            tooltipY = 15;
        }

        setTooltip({
            visible: true,
            x: tooltipX,
            y: tooltipY,
            side,
            index,
        });
    }, []);

    const handleMouseLeave = useCallback(() => {
        setTooltip(prevState => ({
            ...prevState,
            visible: false,
            index: -1,
        }));
    }, []);

    const calculateTooltipValues = useCallback((side: 'buy' | 'sell', index: number) => {
        const orders = side === 'buy' ? buyOrders : sellOrders;
        return calculateValues(orders, index);
    }, [buyOrders, sellOrders]);

    return {
        tooltip,
        handleMouseEnter,
        handleMouseLeave,
        calculateTooltipValues
    };
}
