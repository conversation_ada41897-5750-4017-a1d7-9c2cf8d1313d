import { useMemo } from 'react';
import { DataFrame, FieldType, dateTime } from '@grafana/data';

interface UseTimeSeriesResult {
    timeSeriesFrames: DataFrame[];
    lastUpdateTime: string;
}

export function useTimeSeries(dataSeries: DataFrame[]): UseTimeSeriesResult {
    // Process data into time series frames for the graph
    const { timeSeriesFrames, lastUpdateTime } = useMemo(() => {
        if (dataSeries.length === 0) {
            return { timeSeriesFrames: [], lastUpdateTime: '' };
        }

        // Check for trading data format first (state_reports table)
        const tradingDataFrame = dataSeries.find(frame => {
            const dataField = frame.fields.find(f => f.name === 'data');
            return dataField && dataField.values.length > 0;
        });

        if (tradingDataFrame) {
            // Extract timestamp from ts field in trading data
            const tsField = tradingDataFrame.fields.find(field =>
                field.name === 'ts' || field.type === FieldType.time
            );

            const dataField = tradingDataFrame.fields.find(field =>
                field.name === 'data'
            );

            if (!tsField || !dataField || tsField.values.length === 0) {
                return { timeSeriesFrames: [], lastUpdateTime: '' };
            }

            // Build time series with midPrice calculated from ask/bid
            const values: Array<{time: number, midPrice: number | null}> = [];

            for (let i = 0; i < tsField.values.length; i++) {
                const timestamp = tsField.values.get(i);
                const dataValue = dataField.values.get(i);

                if (!timestamp) continue;

                let tradingData;
                if (typeof dataValue === 'string') {
                    try {
                        tradingData = JSON.parse(dataValue);
                    } catch {
                        continue;
                    }
                } else if (typeof dataValue === 'object' && dataValue !== null) {
                    tradingData = dataValue;
                }

                if (tradingData) {
                    // Get best ask and bid prices if available
                    const bestAsk = tradingData.asks && tradingData.asks.length > 0 ?
                        tradingData.asks[0].price : null;
                    const bestBid = tradingData.bids && tradingData.bids.length > 0 ?
                        tradingData.bids[0].price : null;

                    // Calculate midPrice only if both ask and bid are available
                    let midPrice = null;
                    if (bestAsk !== null && bestBid !== null) {
                        midPrice = (bestAsk + bestBid) / 2;
                    }

                    // Add to values if we have a valid midPrice
                    if (midPrice !== null) {
                        values.push({
                            time: timestamp,
                            midPrice: midPrice
                        });
                    }
                }
            }

            // Sort values by timestamp (ascending order)
            values.sort((a, b) => a.time - b.time);

            // Set last update time
            let lastTime = '';
            if (values.length > 0) {
                lastTime = dateTime(values[values.length - 1].time).format('YYYY-MM-DD HH:mm:ss');
            }

            // If we have no values, return empty frames
            if (values.length === 0) {
                return { timeSeriesFrames: [], lastUpdateTime: lastTime };
            }

            // Create series for time and midPrice
            const times = values.map(v => v.time);
            const midPrices = values.map(v => v.midPrice);

            // Create time field with proper configuration
            const timeField = {
                name: 'time',
                type: FieldType.time,
                values: times,
                config: {
                    displayName: 'Time',
                }
            };

            // Create midPrice field with enhanced configuration
            const midPriceField = {
                name: 'midPrice',
                type: FieldType.number,
                values: midPrices,
                config: {
                    displayName: 'Mid Price',
                    color: { mode: 'fixed', fixedColor: '#5794F2' },
                    custom: {
                        drawStyle: 'line',
                        lineInterpolation: 'linear',
                        lineWidth: 2,
                        fillOpacity: 10,
                        pointSize: 5,
                        showPoints: 'auto',
                        axisPlacement: 'auto',
                        lineStyle: { fill: 'solid' },
                        spanNulls: true,
                    },
                    min: null,
                    max: null,
                    unit: 'none',
                }
            };

            // Create frame for the timeseries component with enhanced field config
            const frames = [{
                name: 'Orderbook Snapshots',
                fields: [timeField, midPriceField],
                length: times.length
            }];

            return {
                timeSeriesFrames: frames,
                lastUpdateTime: lastTime
            };
        }

        // If not trading data, check for market_snapshots format
        const snapshotFrame = dataSeries.find(series =>
            series.fields.some(field => field.name === 'ts') &&
            (series.fields.some(field => field.name === 'best_ask') ||
                series.fields.some(field => field.name === 'best_bid'))
        );

        if (!snapshotFrame) {
            return { timeSeriesFrames: [], lastUpdateTime: '' };
        }

        // Extract the necessary fields
        const timeField = snapshotFrame.fields.find(field =>
            field.name === 'ts' || field.type === FieldType.time
        );
        const askField = snapshotFrame.fields.find(field =>
            field.name === 'best_ask'
        );
        const bidField = snapshotFrame.fields.find(field =>
            field.name === 'best_bid'
        );

        if (!timeField || (!askField && !bidField)) {
            return { timeSeriesFrames: [], lastUpdateTime: '' };
        }

        // Set the last update time for display
        let lastTime = '';
        if (timeField.values.length > 0) {
            const lastTimeValue = timeField.values[timeField.values.length - 1];
            lastTime = dateTime(lastTimeValue).format('YYYY-MM-DD HH:mm:ss');
        }

        // Create time field with proper configuration
        const timeFieldConfig = {
            name: 'time',
            type: FieldType.time,
            values: timeField.values,
            config: {
                displayName: 'Time',
            }
        };

        // Calculate mid price if both ask and bid fields are available
        const midPrices = [];
        if (askField && bidField) {
            for (let i = 0; i < timeField.values.length; i++) {
                const askPrice = askField.values.get(i);
                const bidPrice = bidField.values.get(i);

                if (askPrice !== null && bidPrice !== null) {
                    midPrices.push((askPrice + bidPrice) / 2);
                } else {
                    midPrices.push(null);
                }
            }
        } else if (askField) {
            // If only ask is available, use that
            for (let i = 0; i < timeField.values.length; i++) {
                midPrices.push(askField.values.get(i));
            }
        } else if (bidField) {
            // If only bid is available, use that
            for (let i = 0; i < timeField.values.length; i++) {
                midPrices.push(bidField.values.get(i));
            }
        }

        const midPriceField = {
            name: 'midPrice',
            type: FieldType.number,
            values: midPrices,
            config: {
                displayName: 'Mid Price',
                color: { mode: 'fixed', fixedColor: '#5794F2' },
                custom: {
                    drawStyle: 'line',
                    lineInterpolation: 'linear',
                    lineWidth: 2,
                    fillOpacity: 10,
                    pointSize: 5,
                    showPoints: 'auto',
                    axisPlacement: 'auto',
                    lineStyle: { fill: 'solid' },
                    spanNulls: true,
                },
                min: null,
                max: null,
                unit: 'none',
            }
        };

        const frames = [{
            name: 'Orderbook Snapshots',
            fields: [timeFieldConfig, midPriceField],
            length: timeField.values.length
        }];

        return {
            timeSeriesFrames: frames,
            lastUpdateTime: lastTime
        };
    }, [dataSeries]);

    return {
        timeSeriesFrames,
        lastUpdateTime
    };
}
