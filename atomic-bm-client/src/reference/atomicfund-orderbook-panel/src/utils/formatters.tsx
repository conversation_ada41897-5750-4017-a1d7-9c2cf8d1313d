/**
 * Utility functions for formatting orderbook data
 */

/**
 * Format price with exactly 4 decimal places for all values
 */
export function formatPrice(price: number, decimalPlaces: number): string {
    // For zero or invalid values, just use the configured decimal places
    if (!price || isNaN(price)) {
        return (0).toFixed(decimalPlaces);
    }

    // Get the absolute value for calculations
    const absValue = Math.abs(price);

    // Calculate significant digits needed for small values
    let effectiveDecimals = decimalPlaces;

    if (absValue < 0.01) {
        // Find the position of the first non-zero digit after decimal
        const firstSignificantPos = -Math.floor(Math.log10(absValue));

        // Add 3 more digits after the first significant digit (to show variation)
        effectiveDecimals = Math.max(decimalPlaces, firstSignificantPos + 3);

        // Cap at a reasonable maximum to prevent extremely long numbers
        effectiveDecimals = Math.min(effectiveDecimals, 8);
    }

    // Format with calculated decimal places
    const formattedPrice = price.toFixed(effectiveDecimals);

    // Apply suffixes for larger values (while maintaining precision)
    if (absValue >= 1000000000) {
        return `${(price / 1000000000).toFixed(Math.min(effectiveDecimals, 4))}B`;
    } else if (absValue >= 1000000) {
        return `${(price / 1000000).toFixed(Math.min(effectiveDecimals, 4))}M`;
    }

    return addThousandsSeparator(formattedPrice.replace(/\.0+$/, ''));
}

/**
 * Format amount with consistent decimal places and thousands separators
 */
export function formatAmount(amount: number, decimalPlaces: number): string {
    // For zero or invalid values, just return "0"
    if (!amount || isNaN(amount)) {
        return "0";
    }

    // Get the absolute value for calculations
    const absValue = Math.abs(amount);

    // Handle large values with magnitude-based precision
    if (absValue >= 1000000000) {
        // For billions, show only 2 decimals
        return `${(amount / 1000000000).toFixed(2).replace(/\.0+$/, '')}B`;
    } else if (absValue >= 1000000) {
        // For millions, show only 2 decimals
        return `${(amount / 1000000).toFixed(2).replace(/\.0+$/, '')}M`;
    } else if (absValue >= 10) {
        // Add thousands separators for numbers >= 10
        return addThousandsSeparator(amount.toFixed(2).replace(/\.0+$/, ''));
    }

    // For very small non-zero values, use scientific notation or adaptive precision
    if (absValue > 0 && absValue < 0.0001) {
        // For extremely small values, use scientific notation
        if (absValue < 0.00000001) {
            return amount.toExponential(2);
        }

        // Find the position of the first non-zero digit after decimal
        const str = absValue.toString();
        const match = str.match(/\.0*/);
        if (match) {
            const leadingZeros = match[0].length - 1; // -1 for the decimal point
            // Show at least 2 significant digits after the first non-zero digit
            const sigDigits = leadingZeros + 2;
            return amount.toFixed(sigDigits);
        }
    }

    // For regular values between 0.0001 and 1
    if (absValue < 1) {
        // Use at least the default decimal places, but ensure we show meaningful digits
        return amount.toFixed(Math.max(decimalPlaces, 5));
    }

    // For regular values, use appropriate decimals but trim trailing zeros
    return addThousandsSeparator(amount.toFixed(decimalPlaces).replace(/\.0+$/, ''));
}

/**
 * Add thousands separators to a number string
 */
const addThousandsSeparator = (numStr: string): string => {
    // Check if there's a decimal part
    const parts = numStr.split('.');

    // Format the integer part with commas
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // Rejoin with the decimal part if it exists
    return parts.length > 1 ? parts.join('.') : parts[0];
}

/**
 * Format total values with appropriate units (M, B) and always 2 decimal places
 */
export function formatTotal(total: number, decimalPlaces: number): string {
    // For totals less than 1, show 4 decimal places (0.xxxxx)
    if (total > 0 && total < 1) {
        return total.toFixed(5);
    }

    if (total >= 1000000000) {
        return `${(total / 1000000000).toFixed(2)}B`;
    } else if (total >= 1000000) {
        return `${(total / 1000000).toFixed(2)}M`;
    }

    return addThousandsSeparator(total.toFixed(decimalPlaces).replace(/\.0+$/, ''));
}

/**
 * Format timestamp to readable date string
 */
export function formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toISOString().replace('T', ' ').substring(0, 19);
}
