import { OrderEntry } from '../types';

/**
 * Calculate cumulative values for tooltip display
 */
export function calculateTooltipValues(
    orders: OrderEntry[],
    index: number
): { avgPrice: number; sumBaseAmount: number; sumQuoteValue: number } {
    if (index < 0 || !orders || orders.length === 0) {
        return { avgPrice: 0, sumBaseAmount: 0, sumQuoteValue: 0 };
    }

    const selectedOrders = orders.slice(0, index + 1);

    let sumBaseAmount = 0;
    let sumQuoteValue = 0;

    selectedOrders.forEach(order => {
        sumBaseAmount += order.amount;
        sumQuoteValue += order.total;
    });

    const avgPrice = sumBaseAmount > 0 ? sumQuoteValue / sumBaseAmount : 0;

    return { avgPrice, sumBaseAmount, sumQuoteValue };
}
