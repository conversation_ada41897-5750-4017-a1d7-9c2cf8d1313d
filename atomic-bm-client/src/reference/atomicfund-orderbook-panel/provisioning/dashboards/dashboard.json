{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 20, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"baseAsset": "BTC", "decimalPlaces": {"amount": 5, "price": 2, "total": 2}, "depthVisualization": "amount", "enablePlayback": true, "enableTimeNavigation": true, "exchange": "binance", "highlightOpenOrders": true, "maxOrders": 15, "quoteAsset": "USDT", "seriesCountSize": "sm", "showLastUpdate": true, "showSeriesCount": false, "showSummary": true, "showTradingMetrics": true, "text": "Default value of text input option", "tradingMetricsPosition": "top"}, "pluginVersion": "11.5.2", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "editorType": "sql", "format": 1, "meta": {"builderOptions": {"columns": [], "database": "mktd", "limit": 1000, "mode": "list", "queryType": "table", "table": "market_snapshots"}}, "pluginVersion": "4.8.2", "queryType": "table", "rawSql": "SELECT\n    ts,\n    bot_id,\n    account_id,\n    exchange,\n    base,\n    quote,\n    data\nFROM trading.state_reports\nWHERE bot_id = '${bot_id}' \n    AND exchange = '${exchange}' \n    AND account_id = '${account_id}'  \n    AND base = '${base}' \n    AND quote = '${quote}'\n    AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n    AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))  \nORDER BY ts DESC", "refId": "A"}], "title": "", "type": "atomicfund-orderbook-panel"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": ["trading", "orderbook", "crypto"], "templating": {"list": [{"current": {"text": "mmaas_maker", "value": "mmaas_maker"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "definition": "SELECT DISTINCT bot_id FROM trading.state_reports WHERE ts >= FROM_UNIXTIME(intDiv(${__from}, 1000)) AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "label": "Bot", "name": "bot_id", "options": [], "query": "SELECT DISTINCT bot_id FROM trading.state_reports WHERE ts >= FROM_UNIXTIME(intDiv(${__from}, 1000)) AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "concordium", "value": "concordium"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "definition": "SELECT DISTINCT account_id FROM trading.state_reports WHERE bot_id = '${bot_id}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "label": "Account", "name": "account_id", "options": [], "query": "SELECT DISTINCT account_id FROM trading.state_reports WHERE bot_id = '${bot_id}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "BitMart", "value": "BitMart"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "definition": "SELECT DISTINCT exchange FROM trading.state_reports WHERE bot_id = '${bot_id}' AND account_id = '${account_id}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "label": "Exchange", "name": "exchange", "options": [], "query": "SELECT DISTINCT exchange FROM trading.state_reports WHERE bot_id = '${bot_id}' AND account_id = '${account_id}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "CCD", "value": "CCD"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "definition": "SELECT DISTINCT base FROM trading.state_reports WHERE bot_id = '${bot_id}' AND account_id = '${account_id}' AND exchange = '${exchange}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "description": "", "label": "Base", "name": "base", "options": [], "query": "SELECT DISTINCT base FROM trading.state_reports WHERE bot_id = '${bot_id}' AND account_id = '${account_id}' AND exchange = '${exchange}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "USDT", "value": "USDT"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "grafana-clickhouse-datasource"}, "definition": "SELECT DISTINCT quote FROM trading.state_reports WHERE bot_id = '${bot_id}' AND account_id = '${account_id}' AND exchange = '${exchange}' AND base = '${base}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "description": "", "label": "Quote", "name": "quote", "options": [], "query": "SELECT DISTINCT quote FROM trading.state_reports WHERE bot_id = '${bot_id}' AND account_id = '${account_id}' AND exchange = '${exchange}' AND base = '${base}'\n  AND ts >= FROM_UNIXTIME(intDiv(${__from}, 1000))\n  AND ts <= FROM_UNIXTIME(intDiv(${__to}, 1000))", "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Crypto Orderbook Dashboard", "uid": "a538aeff-5a8a-42a5-901c-938d896fdd6f", "version": 1, "weekStart": ""}