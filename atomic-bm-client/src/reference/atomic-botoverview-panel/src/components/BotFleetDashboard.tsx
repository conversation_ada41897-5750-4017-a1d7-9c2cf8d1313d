import React, { useState, useEffect } from 'react';
import { PanelProps } from '@grafana/data';
import { css } from '@emotion/css';
import { useStyles2, LoadingPlaceholder, MultiSelect } from '@grafana/ui';
import { SelectableValue } from '@grafana/data';
import { MemoizedBotCard } from './BotCard';
import { DashboardSummary } from './DashboardSummary';
import { BotFleetOptions, Bot } from '../types';
import { processBotData } from '../data/BotDataProcessor';
import { FixedSizeGrid as Grid } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';

interface Props extends PanelProps<BotFleetOptions> {}

// Updated type for the filters with multi-select support
interface FilterState {
    exchanges: string[];
    assets: string[];
    accounts: string[];
    botIds: string[];
}

export const BotFleetDashboard: React.FC<Props> = ({ options, data, width, height }) => {
    const styles = useStyles2(getStyles);
    const [filteredBots, setFilteredBots] = useState<Bot[]>([]);
    const [allBots, setAllBots] = useState<Bot[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    // Updated filters state for multi-select
    const [filters, setFilters] = useState<FilterState>({
        exchanges: [],
        assets: [],
        accounts: [],
        botIds: []
    });

    // Process data when it changes
    useEffect(() => {
        if (data && data.series) {
            setIsLoading(true);
            setError(null);

            try {
                // Log some basic information about the data
                console.log(`Received ${data.series.length} data series frames`);

                // For each series, log basic info
                data.series.forEach((series, index) => {
                    console.log(`Series ${index}: ${series.name || 'Unnamed'}, ${series.length} rows, ${series.fields.length} fields`);
                    console.log(`Fields: ${series.fields.map(f => f.name).join(', ')}`);
                });

                const processedData = processBotData(data.series);
                console.log('Processed bot data:', processedData);

                if (processedData.length === 0) {
                    console.warn('No bots were processed from the data');
                }

                setAllBots(processedData);

                // Apply any active filters
                const filtered = applyFilters(processedData, filters);
                setFilteredBots(filtered);
            } catch (error) {
                console.error('Error processing data:', error);
                const errorMessage = error instanceof Error ? error.message : String(error);
                setError('Failed to process bot data: ' + errorMessage);
            } finally {
                setIsLoading(false);
            }
        }
    }, [data]);

    // Reapply filters when they or the bot data changes
    useEffect(() => {
        const filtered = applyFilters(allBots, filters);
        setFilteredBots(filtered);
    }, [filters, allBots]);

    // Handle filter changes
    const handleFilterChange = (filterType: keyof FilterState, values: string[]) => {
        console.log(`Updating ${filterType} filter:`, values);
        setFilters(prev => ({
            ...prev,
            [filterType]: values
        }));
    };

    // Extract available options for filters
    const availableExchanges = [...new Set(allBots.filter(bot => bot.exchange).map(bot => bot.exchange))];
    const availableBotIds = [...new Set(allBots.filter(bot => bot.id).map(bot => bot.id))];
    const availableAccounts = [...new Set(allBots.filter(bot => bot.account).map(bot => bot.account))];

    // Combined list of all base and quote assets
    const availableAssets = [...new Set([
        ...allBots.filter(bot => bot.baseAsset).map(bot => bot.baseAsset),
        ...allBots.filter(bot => bot.quoteAsset).map(bot => bot.quoteAsset)
    ])];

    // Render bot cards with virtualization
    const renderBotCards = () => {
        if (filteredBots.length === 0) {
            return (
                <div className={styles.noDataMessage}>
                    {allBots.length > 0 ?
                        'No bots match the current filters. Try changing or clearing the filters.' :
                        'No bot data found. Check your data source configuration.'}
                </div>
            );
        }

        return (
            <div className={styles.botGridContainer}>
                <AutoSizer>
                    {({ height: autoHeight, width: autoWidth }) => {
                        // Calculate how many columns can fit based on width
                        const columnWidth = 320; // Width of each card
                        const columnCount = Math.max(1, Math.floor(autoWidth / columnWidth));
                        const rowCount = Math.ceil(filteredBots.length / columnCount);

                        return (
                            <Grid
                                className={styles.botGrid}
                                columnCount={columnCount}
                                columnWidth={autoWidth / columnCount}
                                height={autoHeight}
                                rowCount={rowCount}
                                rowHeight={325} // Fixed card height + spacing
                                width={autoWidth}
                                itemData={{
                                    bots: filteredBots,
                                    columnCount
                                }}
                                overscanRowCount={2}
                            >
                                {({ columnIndex, rowIndex, style, data }) => {
                                    const { bots, columnCount } = data;
                                    const index = rowIndex * columnCount + columnIndex;

                                    if (index >= bots.length) {
                                        return null;
                                    }

                                    return (
                                        <div style={style} className={styles.botCardWrapper}>
                                            <MemoizedBotCard bot={bots[index]} />
                                        </div>
                                    );
                                }}
                            </Grid>
                        );
                    }}
                </AutoSizer>
            </div>
        );
    };

    // If there's an error, display it
    if (error) {
        return (
            <div className={styles.errorWrapper}>
                <div className={styles.errorMessage}>
                    <h3>Error Loading Bot Data</h3>
                    <p>{error}</p>
                    <p>Check browser console for more details.</p>
                </div>
            </div>
        );
    }

    // Create selectable options for dropdowns
    const createSelectOptions = (items: string[]): Array<SelectableValue<string>> => {
        return items.map(item => ({ label: item, value: item }));
    };

    return (
        <div className={styles.wrapper}>
            <div className={styles.filterBar}>
                <div className={styles.filterGroup}>
                    <label className={styles.filterLabel}>Bot ID</label>
                    <MultiSelect
                        width={40}
                        options={createSelectOptions(availableBotIds)}
                        value={filters.botIds}
                        onChange={(values) => handleFilterChange('botIds', values.map(v => v.value || ''))}
                        placeholder="All Bot IDs"
                        isClearable={true}
                        allowCustomValue
                    />
                </div>

                <div className={styles.filterGroup}>
                    <label className={styles.filterLabel}>Account</label>
                    <MultiSelect
                        width={30}
                        options={createSelectOptions(availableAccounts)}
                        value={filters.accounts}
                        onChange={(values) => handleFilterChange('accounts', values.map(v => v.value || ''))}
                        placeholder="All Accounts"
                        isClearable={true}
                    />
                </div>

                <div className={styles.filterGroup}>
                    <label className={styles.filterLabel}>Exchange</label>
                    <MultiSelect
                        width={30}
                        options={createSelectOptions(availableExchanges)}
                        value={filters.exchanges}
                        onChange={(values) => handleFilterChange('exchanges', values.map(v => v.value || ''))}
                        placeholder="All Exchanges"
                        isClearable={true}
                    />
                </div>

                <div className={styles.filterGroup}>
                    <label className={styles.filterLabel}>Asset</label>
                    <MultiSelect
                        width={30}
                        options={createSelectOptions(availableAssets)}
                        value={filters.assets}
                        onChange={(values) => handleFilterChange('assets', values.map(v => v.value || ''))}
                        placeholder="All Assets"
                        isClearable={true}
                    />
                </div>
            </div>

            <DashboardSummary bots={filteredBots} />

            {isLoading ? (
                <div className={styles.loadingWrapper}>
                    <LoadingPlaceholder text="Loading bot data..." />
                </div>
            ) : (
                renderBotCards()
            )}
        </div>
    );
};

// Updated helper function to apply filters with multi-select support
function applyFilters(bots: Bot[], filters: FilterState): Bot[] {
    return bots.filter(bot => {
        // Check if the bot matches any of the selected exchanges (or all if none selected)
        const matchesExchange = filters.exchanges.length === 0 ||
            (bot.exchange && filters.exchanges.includes(bot.exchange));

        // Check if the bot matches any of the selected assets (base or quote)
        const matchesAsset = filters.assets.length === 0 ||
            (bot.baseAsset && filters.assets.includes(bot.baseAsset)) ||
            (bot.quoteAsset && filters.assets.includes(bot.quoteAsset));

        // Check if the bot matches any of the selected accounts
        const matchesAccount = filters.accounts.length === 0 ||
            (bot.account && filters.accounts.includes(bot.account));

        // Check if the bot matches any of the selected bot IDs
        const matchesBotId = filters.botIds.length === 0 ||
            (bot.id && filters.botIds.includes(bot.id));

        return matchesExchange && matchesAsset && matchesAccount && matchesBotId;
    });
}

const getStyles = () => ({
    wrapper: css`
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #0b0f19;
    color: #fff;
    overflow: hidden;
  `,
    filterBar: css`
    display: flex;
    gap: 12px;
    padding: 12px 16px;
    background-color: #121722;
    align-items: flex-end;
    flex-wrap: wrap;
  `,
    filterGroup: css`
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 150px;
  `,
    filterLabel: css`
    font-size: 12px;
    font-weight: 500;
    color: #9096a1;
  `,
    botGridContainer: css`
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: hidden;
  `,
    botGrid: css`
    padding: 12px;
  `,
    botCardWrapper: css`
    padding: 6px;
    box-sizing: border-box;
  `,
    noDataMessage: css`
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #9096a1;
    font-size: 16px;
    text-align: center;
    padding: 0 20px;
  `,
    loadingWrapper: css`
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    flex: 1;
  `,
    errorWrapper: css`
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #0b0f19;
    color: #fff;
    padding: 20px;
  `,
    errorMessage: css`
    background-color: rgba(234, 0, 112, 0.1);
    border: 1px solid #ea0070;
    border-radius: 4px;
    padding: 20px;
    max-width: 80%;
    text-align: center;
    
    h3 {
      color: #ea0070;
      margin-top: 0;
    }
  `,
});
