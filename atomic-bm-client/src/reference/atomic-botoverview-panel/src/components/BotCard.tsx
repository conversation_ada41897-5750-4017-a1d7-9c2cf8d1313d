import React, { memo } from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { Bot, ExecStatTimeframe } from '../types';
import { LocationService } from '@grafana/runtime';

interface BotCardProps {
    bot: Bot;
    targetDashboardUid?: string;
    targetDashboardName?: string;
    timeframeOption?: string;
    locationService?: LocationService;
}

const BotCard: React.FC<BotCardProps> = ({
                                             bot,
                                             targetDashboardUid = 'eefg6q9w63h8gd',
                                             targetDashboardName = 'bot-state',
                                             timeframeOption = '12h',
                                             locationService
                                         }) => {
    const styles = useStyles2(getStyles);

    // Function to get the prioritized timeframe data
    const getTimeframeData = (execStats?: ExecStatTimeframe[]): ExecStatTimeframe | null => {
        if (!execStats || execStats.length === 0) return null;

        // Look for 24h timeframe (86400000000000)
        const dayFrame = execStats.find(stat => stat.time_frame === 86400000000000);
        if (dayFrame && (dayFrame.buy.volume > 0 || dayFrame.sell.volume > 0)) {
            return dayFrame;
        }

        // Look for 1h timeframe (3600000000000)
        const hourFrame = execStats.find(stat => stat.time_frame === 3600000000000);
        if (hourFrame && (hourFrame.buy.volume > 0 || hourFrame.sell.volume > 0)) {
            return hourFrame;
        }

        // Look for any non-empty timeframe
        return execStats.find(stat => stat.buy.volume > 0 || stat.sell.volume > 0) || null;
    };

    // Get the appropriate timeframe data
    const timeframeData = getTimeframeData(bot.execStats);

    // Get timeframe label for display
    const getTimeframeLabel = (timeframe: number): string => {
        switch (timeframe) {
            case 86400000000000: return '24H TRADING ACTIVITY';
            case 3600000000000: return '1H TRADING ACTIVITY';
            case 300000000000: return '5M TRADING ACTIVITY';
            case 604800000000000: return '7D TRADING ACTIVITY';
            case 43200000000000: return '12H TRADING ACTIVITY';
            default: return 'TRADING ACTIVITY';
        }
    };

    /**
     * Format amount with consistent decimal places and thousands separators
     */
    const formatAmount = (amount: number, decimalPlaces: number): string => {
        // For zero or invalid values, just return "0"
        if (!amount || isNaN(amount)) {
            return "0";
        }

        // Get the absolute value for calculations
        const absValue = Math.abs(amount);

        // Handle large values with magnitude-based precision
        if (absValue >= 1000000000) {
            // For billions, show only 2 decimals
            return `${(amount / 1000000000).toFixed(2).replace(/\.0+$/, '')}B`;
        } else if (absValue >= 1000000) {
            // For millions, show only 2 decimals
            return `${(amount / 1000000).toFixed(2).replace(/\.0+$/, '')}M`;
        } else if (absValue >= 10) {
            // Add thousands separators for numbers >= 10
            return addThousandsSeparator(amount.toFixed(2).replace(/\.0+$/, ''));
        }

        // For very small non-zero values, use scientific notation or adaptive precision
        if (absValue > 0 && absValue < 0.0001) {
            // For extremely small values, use scientific notation
            if (absValue < 0.00000001) {
                return amount.toExponential(2);
            }

            // Find the position of the first non-zero digit after decimal
            const str = absValue.toString();
            const match = str.match(/\.0*/);
            if (match) {
                const leadingZeros = match[0].length - 1; // -1 for the decimal point
                // Show at least 2 significant digits after the first non-zero digit
                const sigDigits = leadingZeros + 2;
                return amount.toFixed(sigDigits);
            }
        }

        // For regular values between 0.0001 and 1
        if (absValue < 1) {
            // Use at least the default decimal places, but ensure we show meaningful digits
            return amount.toFixed(Math.max(decimalPlaces, 5));
        }

        // For regular values, use appropriate decimals but trim trailing zeros
        return addThousandsSeparator(amount.toFixed(decimalPlaces).replace(/\.0+$/, ''));
    }

    /**
     * Add thousands separators to a number string
     */
    const addThousandsSeparator = (numStr: string): string => {
        // Check if there's a decimal part
        const parts = numStr.split('.');

        // Format the integer part with commas
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        // Rejoin with the decimal part if it exists
        return parts.length > 1 ? parts.join('.') : parts[0];
    }

    // Format spread as percentage
    const formatSpread = (spread?: number) => {
        if (spread === undefined || spread === null) return '-';

        // Format the spread with 3 decimal places
        const formattedSpread = (spread).toFixed(3) + '%';

        // Use text-align in CSS instead of padding for alignment
        return formattedSpread;
    };

    // Calculate inventory value (Stock * Best Ref Bid)
    const calculateInventoryValue = (): number => {
        return (bot.stockBalance || 0) * (bot.buyRefPrice || 0);
    };

    // Get the appropriate order badge style based on the order count
    const getOrdersBadgeStyle = (orderCount: number) => {
        // Yellow for 0 orders, green for orders > 0
        return orderCount === 0
            ? css`
                background-color: rgba(255, 196, 0, 0.1);
                color: #ffc400;
                border-radius: 3px;
                padding: 3px 6px;
                font-size: 11px;
              `
            : css`
                background-color: rgba(0, 177, 100, 0.1);
                color: #00b164;
                border-radius: 3px;
                padding: 3px 6px;
                font-size: 11px;
              `;
    };

    // Handle click to navigate to the detailed dashboard
    const handleCardClick = () => {
        // Extract bot information for the URL parameters
        const { id, exchange, baseAsset, quoteAsset, account } = bot;

        // Construct the URL with query parameters
        const url = `/d/${targetDashboardUid}/${targetDashboardName}?` +
            `var-timeframe=${timeframeOption}` +
            `&orgId=1` +
            `&from=now-5m&to=now` +
            `&timezone=browser` +
            `&var-bot_id=${encodeURIComponent(id)}` +
            `&var-account_id=${encodeURIComponent(account || '')}` +
            `&var-exchange=${encodeURIComponent(exchange || '')}` +
            `&var-base=${encodeURIComponent(baseAsset || '')}` +
            `&var-quote=${encodeURIComponent(quoteAsset || '')}`;

        // Navigate to the URL
        if (locationService) {
            // Use Grafana's location service if available
            locationService.push(url);
        } else {
            // Fallback to standard window.location
            window.open(url, '_blank');
        }
    };

    return (
        <div className={styles.card} onClick={handleCardClick}>
            {/* Header section */}
            <div className={styles.header}>
                <div className={styles.botInfo}>
                    <div className={styles.botIdRow}>
                        <div className={styles.botId}>{bot.id}</div>
                        <div className={styles.tradingPair}>{bot.pair}</div>
                    </div>

                    <div className={styles.badgeContainer}>
                        {bot.exchange && <span className={styles.exchangeBadge}>{bot.exchange}</span>}
                        {bot.account && <span className={styles.accountBadge}>{bot.account}</span>}
                        <span className={getOrdersBadgeStyle(bot.activeOrders || 0)}>{bot.activeOrders || 0} Orders</span>
                    </div>
                </div>
            </div>

            {/* Improved Balance section */}
            <div className={styles.balanceRow}>
                <div className={styles.balanceItem}>
                    <div className={styles.balanceLabel}>Stock</div>
                    <div className={styles.balanceValue} title={`${formatAmount(bot.stockBalance, 2)} ${bot.baseAsset}`}>
                        {formatAmount(bot.stockBalance, 2)} <span className={styles.assetSymbol}>{bot.baseAsset}</span>
                    </div>
                </div>
                <div className={styles.balanceItem}>
                    <div className={styles.balanceLabel}>Value</div>
                    <div className={styles.balanceValue} title={`${formatAmount(calculateInventoryValue(), 2)} ${bot.quoteAsset}`}>
                        {formatAmount(calculateInventoryValue(), 2)} <span className={styles.assetSymbol}>{bot.quoteAsset}</span>
                    </div>
                </div>
                <div className={styles.balanceItem}>
                    <div className={styles.balanceLabel}>Money</div>
                    <div className={styles.balanceValue} title={`${formatAmount(bot.moneyBalance, 2)} ${bot.quoteAsset}`}>
                        {formatAmount(bot.moneyBalance, 2)} <span className={styles.assetSymbol}>{bot.quoteAsset}</span>
                    </div>
                </div>
            </div>

            {/* Trading Activity section with "No data" fallback */}
            <div className={styles.activityRow}>
                <div className={styles.activityHeader}>
                    {timeframeData ? getTimeframeLabel(timeframeData.time_frame) : 'TRADING ACTIVITY'}
                </div>

                {timeframeData ? (
                    <div className={styles.activityGrid}>
                        {/* Buy side stats */}
                        {timeframeData.buy.volume > 0 && (
                            <div className={styles.activityItem}>
                                <div className={styles.activityLabel}>
                                    <span className={styles.buyIndicator}>Buy</span>
                                </div>
                                <div className={styles.activityValue} title={`Volume: ${formatAmount(timeframeData.buy.volume, 5)} ${bot.baseAsset}`}>
                                    {formatAmount(timeframeData.buy.volume, 5)} <span className={styles.assetSymbol}>{bot.baseAsset}</span>
                                </div>
                                <div className={styles.activitySubValue} title={`Value: ${formatAmount(timeframeData.buy.money_volume, 2)} ${bot.quoteAsset}`}>
                                    {formatAmount(timeframeData.buy.money_volume, 2)} <span className={styles.assetSymbol}>{bot.quoteAsset}</span>
                                </div>
                            </div>
                        )}

                        {/* Sell side stats */}
                        {timeframeData.sell.volume > 0 && (
                            <div className={styles.activityItem}>
                                <div className={styles.activityLabel}>
                                    <span className={styles.sellIndicator}>Sell</span>
                                </div>
                                <div className={styles.activityValue} title={`Volume: ${formatAmount(timeframeData.sell.volume, 5)} ${bot.baseAsset}`}>
                                    {formatAmount(timeframeData.sell.volume, 5)} <span className={styles.assetSymbol}>{bot.baseAsset}</span>
                                </div>
                                <div className={styles.activitySubValue} title={`Value: ${formatAmount(timeframeData.sell.money_volume, 2)} ${bot.quoteAsset}`}>
                                    {formatAmount(timeframeData.sell.money_volume, 2)} <span className={styles.assetSymbol}>{bot.quoteAsset}</span>
                                </div>
                            </div>
                        )}

                        {/* Total volume - new section */}
                        {(timeframeData.buy.volume > 0 || timeframeData.sell.volume > 0) && (
                            <div className={styles.activityItem}>
                                <div className={styles.activityLabel}>
                                    <span className={styles.totalIndicator}>Total</span>
                                </div>
                                <div className={styles.activityValue} title={`Total Volume: ${formatAmount(timeframeData.buy.volume + timeframeData.sell.volume, 5)} ${bot.baseAsset}`}>
                                    {formatAmount(timeframeData.buy.volume + timeframeData.sell.volume, 5)} <span className={styles.assetSymbol}>{bot.baseAsset}</span>
                                </div>
                                <div className={styles.activitySubValue} title={`Total Value: ${formatAmount(timeframeData.buy.money_volume + timeframeData.sell.money_volume, 2)} ${bot.quoteAsset}`}>
                                    {formatAmount(timeframeData.buy.money_volume + timeframeData.sell.money_volume, 2)} <span className={styles.assetSymbol}>{bot.quoteAsset}</span>
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className={styles.noActivityData}>
                        No trading activity data available
                    </div>
                )}
            </div>

            {/* Trading metrics */}
            <div className={styles.metricsTable}>
                <div className={styles.tableHeader}>
                    <div className={styles.side}></div>
                    <div className={styles.spreadColumn}>
                        <div className={styles.spreadLabel}>Top Spread</div>
                    </div>
                    <div className={styles.spreadColumn}>
                        <div className={styles.spreadLabel}>Ref Spread</div>
                    </div>
                    <div className={styles.priceColumn}>
                        <div className={styles.priceLabel}>Top Price</div>
                    </div>
                    <div className={styles.priceColumn}>
                        <div className={styles.priceLabel}>Ref Price</div>
                    </div>
                </div>

                {/* Buy Side */}
                <div className={styles.tableRow}>
                    <div className={styles.side}>
                        <span className={styles.buyIndicator}>Buy</span>
                    </div>
                    <div className={styles.spreadColumn}>
                        <div className={styles.spreadValue}>{formatSpread(bot.buyTopSpread)}</div>
                    </div>
                    <div className={styles.spreadColumn}>
                        <div className={styles.spreadValue}>{formatSpread(bot.buyRefSpread)}</div>
                    </div>
                    <div className={styles.priceColumn}>
                        <div className={styles.priceValue}>{formatAmount(bot.buyTopPrice, 2)}</div>
                    </div>
                    <div className={styles.priceColumn}>
                        <div className={styles.priceValue}>{formatAmount(bot.buyRefPrice, 2)}</div>
                    </div>
                </div>

                {/* Sell Side */}
                <div className={styles.tableRow}>
                    <div className={styles.side}>
                        <span className={styles.sellIndicator}>Sell</span>
                    </div>
                    <div className={styles.spreadColumn}>
                        <div className={styles.spreadValue}>{formatSpread(bot.sellTopSpread)}</div>
                    </div>
                    <div className={styles.spreadColumn}>
                        <div className={styles.spreadValue}>{formatSpread(bot.sellRefSpread)}</div>
                    </div>
                    <div className={styles.priceColumn}>
                        <div className={styles.priceValue}>{formatAmount(bot.sellTopPrice, 2)}</div>
                    </div>
                    <div className={styles.priceColumn}>
                        <div className={styles.priceValue}>{formatAmount(bot.sellRefPrice, 2)}</div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const getStyles = () => ({
    card: css`
        background-color: #121722;
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.05);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 315px; /* Fixed height for all cards */
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.1);
        }
    `,
    header: css`
        background-color: #14192a;
        padding: 10px 12px;
        display: flex;
        flex-direction: column;
    `,
    botInfo: css`
        display: flex;
        flex-direction: column;
        gap: 6px;
    `,
    botIdRow: css`
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    `,
    botId: css`
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
    `,
    tradingPair: css`
        font-size: 13px;
        color: #9096a1;
    `,
    badgeContainer: css`
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
    `,
    exchangeBadge: css`
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        padding: 3px 6px;
        font-size: 11px;
    `,
    accountBadge: css`
        background-color: rgba(87, 148, 242, 0.2);
        color: #5794F2;
        border-radius: 3px;
        padding: 3px 6px;
        font-size: 11px;
    `,
    balanceRow: css`
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
        padding: 10px 12px;
        background-color: rgba(0, 0, 0, 0.15);
        gap: 8px;
    `,
    balanceItem: css`
        min-width: 0;
        display: flex;
        flex-direction: column;
    `,
    balanceLabel: css`
        color: #9096a1;
        font-size: 11px;
        margin-bottom: 1px;
    `,
    balanceValue: css`
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `,
    assetSymbol: css`
        opacity: 0.85;
        font-size: 11px;
        margin-left: 1px;
    `,
    activityRow: css`
        padding: 6px 12px 8px;
        background-color: rgba(0, 0, 0, 0.15);
        border-top: 1px solid rgba(255, 255, 255, 0.05);
        height: 96px;
        box-sizing: border-box;
    `,
    activityHeader: css`
        color: #9096a1;
        font-size: 10px;
        margin-bottom: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    `,
    activityGrid: css`
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 8px;
        
        @media (max-width: 350px) {
            grid-template-columns: 1fr;
        }
    `,
    activityItem: css`
        display: flex;
        flex-direction: column;
        min-width: 0;
    `,
    activityLabel: css`
        margin-bottom: 2px;
    `,
    activityValue: css`
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 0;
    `,
    activitySubValue: css`
        font-size: 11px;
        color: #9096a1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `,
    buyIndicator: css`
        color: #00b164;
        font-size: 12px;
        font-weight: 500;
    `,
    sellIndicator: css`
        color: #ea0070;
        font-size: 12px;
        font-weight: 500;
    `,
    totalIndicator: css`
        color: #5794F2;
        font-size: 12px;
        font-weight: 500;
    `,
    noActivityData: css`
        padding: 16px 0;
        color: #9096a1;
        font-size: 12px;
        font-style: italic;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
    `,
    metricsTable: css`
        padding: 8px 12px;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 120px;
    `,
    tableHeader: css`
        display: grid;
        grid-template-columns: 40px 1fr 1fr 1fr 1fr;
        column-gap: 4px;
        margin-bottom: 6px;
        font-size: 10px;
        color: #9096a1;
    `,
    tableRow: css`
        display: grid;
        grid-template-columns: 40px 1fr 1fr 1fr 1fr;
        column-gap: 4px;
        padding: 4px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
    `,
    side: css`
        display: flex;
        align-items: center;
    `,
    spreadColumn: css`
        display: flex;
        justify-content: flex-end;
    `,
    spreadLabel: css`
        font-size: 10px;
        text-align: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `,
    spreadValue: css`
        font-size: 11px;
        text-align: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `,
    priceColumn: css`
        display: flex;
        justify-content: flex-end;
    `,
    priceLabel: css`
        font-size: 10px;
        text-align: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `,
    priceValue: css`
        font-size: 11px;
        font-weight: 500;
        text-align: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `,
});

// Export a memoized version of the component for better performance
export const MemoizedBotCard = memo(BotCard);
export { BotCard };
