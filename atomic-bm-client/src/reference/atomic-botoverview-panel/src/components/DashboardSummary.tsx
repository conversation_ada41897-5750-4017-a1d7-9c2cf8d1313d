import React from 'react';
import { css } from '@emotion/css';
import { useStyles2 } from '@grafana/ui';
import { Bot } from '../types';

interface DashboardSummaryProps {
    bots: Bot[];
}

export const DashboardSummary: React.FC<DashboardSummaryProps> = ({ bots }) => {
    const styles = useStyles2(getStyles);

    // Safely sum up values with null/undefined checks
    const safeSum = (items: any[], property: string): number => {
        return items.reduce((sum, item) => {
            const value = item[property];
            return sum + (value && !isNaN(value) ? value : 0);
        }, 0);
    };

    // Calculate summary statistics
    const totalBots = bots.length;
    const totalActiveOrders = safeSum(bots, 'activeOrders');

    // Format the update time
    const updateTime = new Date().toLocaleString();

    return (
        <div className={styles.container}>
            <div className={styles.item}>Total Bots: <span className={styles.value}>{totalBots}</span></div>
            <div className={styles.item}>Total Active Orders: <span className={styles.value}>{totalActiveOrders}</span></div>
            <div className={styles.updateTime}>Last update: {updateTime}</div>
        </div>
    );
};

const getStyles = () => ({
    container: css`
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background-color: #1a202c;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  `,
    item: css`
    font-size: 16px;
    color: #ffffff;
  `,
    value: css`
    font-weight: 500;
  `,
    updateTime: css`
    font-size: 14px;
    color: #9096a1;
  `
});
