export interface BotFleetOptions {
  // Add any panel options here
}

export interface ExecStatTimeframe {
  buy: {
    avg_price: number;
    money_volume: number;
    volume: number;
  };
  sell: {
    avg_price: number;
    money_volume: number;
    volume: number;
  };
  time_frame: number;
}

export interface Bot {
  id: string;
  exchange: string;
  pair: string;
  baseAsset: string;
  quoteAsset: string;
  account: string;
  activeOrders: number;
  stockBalance: number;
  moneyBalance: number;
  buyTopSpread: number;
  buyRefSpread: number;
  buyTopPrice: number;
  buyRefPrice: number;
  sellTopSpread: number;
  sellRefSpread: number;
  sellTopPrice: number;
  sellRefPrice: number;
  execStats?: ExecStatTimeframe[];
}