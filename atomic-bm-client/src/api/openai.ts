import OpenAI from 'openai';
import { formatCommandsForContent } from '$utils';
import { commands } from '$lib/components/web-terminal/commands';

const openai = new OpenAI({
	apiKey: ''
});

export async function generateAICommandSuggestion(userRequest: string, apiKey: string) {
	try {
		openai.apiKey = apiKey;
		const formattedCommandsString = formatCommandsForContent(commands);
		const completion = await openai.chat.completions.create({
			messages: [
				{
					role: 'system',
					content:
						'You are a helpful assistant who generates commands based on the users requests. You can see the list of commands to analyze which commands suits the users needs.'
				},
				{
					role: 'assistant',
					content:
						'You must explicitly display the command asked by the user, no other text allowed, only the command that is found'
				},
				{
					role: 'assistant',
					content: 'If the user provides any kind of parameters, they should be displayed as well'
				},
				{
					role: 'assistant',
					content:
						'The ideal command display with parameters is: "command <param1> <param2>" or if the user provides parameters: "command param1=1234 param2=3214 param3=5555" or without any parameters: "command"'
				},
				{
					role: 'assistant',
					content:
						'The user can provide parameters by using equal symbols like "param1=1234" or by using spaces like "param1 1234" or "param1: 1234" or "param1:1234" or "param1 1234" or by context like: "param1: 1234 param2: 3214 param3: 5555" or "param1 1234 param2 3214 param3 5555" or "param1=1234 param2=3214 param3=5555"'
				},
				{
					role: 'assistant',
					content:
						'If the user includes any params you must follow this format for the display: "param=1". It should always be "param + equal symbol (=) + value"'
				},
				{
					role: 'assistant',
					content:
						'Take note for situations where user might not explicitly specify the param name but if the context of the command is clear, you can include it'
				},
				{
					role: 'assistant',
					content:
						'For example, a user might want to fetch a single bot by its id and he can type something like "1 bot someIdValue" and that should be interpreted as "bot single botId=someIdValue", the same goes for other commands with similar context'
				},
				{
					role: 'assistant',
					content:
						'Note: the only non-related command text you can display is the message "command not found" if the command is not found'
				},
				{
					role: 'assistant',
					content:
						'Note: Always avoid displaying responses in this format: "Command: command" or "Command: command param1 param2" or "Command: command param1=1234 param2=3214 param3=5555" or "Command: command param1=1234 param2=3214 param3=5555" or "Command: command param1=1234 param2=3214 param3=5555". You must always display the command as it is, without the "Command: " prefix'
				},
				{
					role: 'assistant',
					content: `The existing commands are:\n\n${formattedCommandsString}`
				},
				{
					role: 'user',
					content: userRequest
				}
			],
			model: 'gpt-4'
		});

		return completion.choices[0];
	} catch (error) {
		console.error('Error:', error);
	}
}
