import Axios, { AxiosError, type AxiosRequestConfig } from 'axios';

const getToken = (): string | null => {
    if (typeof window !== 'undefined') {
        return localStorage.getItem('token');
    }
    return null;
};

export const AXIOS_INSTANCE = Axios.create({
    baseURL: import.meta.env.VITE_ATOMIC_BASEURL || ''
});

export const customInstance = <T>(config: AxiosRequestConfig): Promise<T> => {
    const source = Axios.CancelToken.source();
    const token = getToken();

    const headers = {
        ...config.headers,
        ...(token && { Authorization: `Bearer ${JSON.parse(token)}` })
    };

    const promise = AXIOS_INSTANCE({
        ...config,
        headers,
        cancelToken: source.token
    }).then(({ data }) => data);

    // @ts-ignore
    promise.cancel = () => {
        source.cancel('Query was cancelled');
    };

    return promise;
};

export type ErrorType<Error> = AxiosError<Error>;
export type BodyType<BodyData> = BodyData; 