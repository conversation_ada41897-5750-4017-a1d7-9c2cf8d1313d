import { defineConfig } from 'orval';

export default defineConfig({
	botmanager: {
		input: {
			target: './swagger.json',
			converterOptions: {
				refSiblings: 'preserve',
				resolveInternal: true,
				components: true,
				debug: true
			}
		},
		output: {
			baseUrl: '${import.meta.env.VITE_ATOMIC_BASEURL}',
			mode: 'tags',
			prettier: true,
			clean: true,
			headers: true,
			target: './src/client/api.ts',
			client: 'svelte-query',
			mock: false,
			override: {
				mutator: {
					path: './src/utils/api-mutator.ts',
					name: 'customInstance',
				},
				query: {
					useQuery: true,
					usePrefetch: false,
					useInfinite: false,
					useMutation: true,
					signal: false,
					useInfiniteQueryParam: 'false',
					useSuspenseInfiniteQuery: false,
					shouldExportQueryKey: true,
					shouldExportHttpClient: true,
					shouldExportMutatorHooks: true,
					version: 5
				}
			}
		}
	}
});
