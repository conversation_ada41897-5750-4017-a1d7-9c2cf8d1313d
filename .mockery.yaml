issue-845-fix: True
disable-version-string: True
resolve-type-alias: False
packages:
  github.com/herenow/atomic-bm/internal/db/repo/paramrepo:
    interfaces:
      IParams:
        config:
          dir: "internal/db/repo/paramrepo"
          filename: "params_mock.go"
          inpackage: True
          with-expecter: True
        configs:
          - mockname: IParamsMocked
  github.com/herenow/atomic-bm/internal/db/repo/botrepo:
    interfaces:
      IBots:
        config:
          dir: "internal/db/repo/botrepo"
          filename: "bot_mock.go"
          inpackage: True
          with-expecter: True
        configs:
          - mockname: IBotsMocked
  github.com/herenow/atomic-bm/internal/db/repo/accountrepo:
    interfaces:
      IAccountsRepo:
        config:
          dir: "internal/db/repo/accountrepo"
          filename: "account_mock.go"
          inpackage: True
          with-expecter: True
        configs:
          - mockname: IAccountsMocked
  github.com/herenow/atomic-bm/pkg/botregionalclient:
    interfaces:
      IBotRegionalClient:
        config:
          dir: "pkg/botregionalclient"
          filename: "botregion_mock.go"
          inpackage: True
          with-expecter: True
        configs:
          - mockname: IBotRegionalClientMocked