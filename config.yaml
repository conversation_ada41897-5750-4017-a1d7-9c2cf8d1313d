debug: true

database:
  dsn: postgres://postgres:postgres@127.0.0.1:5432/botmanager?sslmode=disable

redis:
  addr: 127.0.0.1:6379

##
## Api Server
##
listen:
  addr: 0.0.0.0:8000

###
## Grpc Servers for Bot Regionals
##
bot_regional_servers:
  - region_id: "local"
    insecure: false
    api_key: "43977ae04b4d887a1517f256ad15f466ac9405b673c1df2da6541a3d44cab0a5"
    bot_regional_addr: "atomic-br.dev.htz-fsn-02.liquidbooks.io:443"

#  bun_debug=0 - disables the hook.
#  bun_debug=1 - enables the hook.
#  bun_debug=2 - enables the hook and verbose mode.
bun_debug: 1

##
## Logging configuration.
##
log:
  # Zap minimal logging level.
  # Valid values: DEBUG, INFO, WARN, ERROR, DPANIC, PANIC, FATAL.
  level: DEBUG

##
## opentelemetry client configuration.
##
open_telemetry:
  service_name: botmanager-local
  bearer_token: "MTmC3AvkOIPwW_HoIF0bMUXD-Ezv71cDqf4k1BwsUGI"
  exporter_url: otel.htz-fsn-02.liquidbooks.io:443 # Grpc OpenTelemetry Collector
  traces:
    enabled: true
    sample_rate: 1.0
  metrics:
    enabled: true
  logs:
    enabled: true

# ParamSecretKey is the secret key used for encrypting and decrypting
# sensitive parameter values, such as 'token' and 'api_secret'.
#
# This key should be a Base64-encoded 256-bit (32-byte) key. It is crucial
# to keep this key secure as it is used for cryptographic operations.
#
# Please generate a strong and unique key using the crypto.NewEncryptionKey function.
param_secret_key: 3i9VCoQmlAU8Fp+XQCcYUn6F1ygpsgdjKgPat0BsFnU=
param_secret_key_version: 1

firebase:
  type: service_account
  project_id: web-push-173c0
  private_key_id: 07a8671a1ef20bdc21367acb264bac1c2d875f2e
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: <EMAIL>
  client_id: 107403196437039388353
  auth_uri: https://accounts.google.com/o/oauth2/auth
  token_uri: https://oauth2.googleapis.com/token
  auth_provider_x509_cert_url: https://www.googleapis.com/oauth2/v1/certs
  client_x509_cert_url: https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-9oq8w%40web-push-173c0.iam.gserviceaccount.com
  universe_domain: googleapis.com

session_manager_cfg:
  idle_timeout: 0
  lifetime: 24
  cleanup_interval: 0
  cookie:
    name: session_id
    domain: localhost
    http_only: true
    path: /
    persist: true
    same_site: 1
    secure: false